<?php

namespace app\modules\backend\controllers;

use app\common\models\CurrencyCourse;
use app\common\models\Orders;
use Yii;

class DashboardController extends BaseController
{
    public function getActions()
    {
        return ['index', 'get-sales-data', 'get-top-products', 'get-top-sellers', 'get-storage-balance', 'get-expenses-type', 'get-monthly-stats'];
    }

    public function actionIndex()
    {
        // По умолчанию - текущий день
        $startDate = Yii::$app->request->get('start_date', date('Y-m-d')) . ' 00:00:00';
        $endDate = Yii::$app->request->get('end_date', date('Y-m-d')) . ' 23:59:59';
        $storageId = Yii::$app->request->get('storage_id');

        $salesData = $this->getSalesData($startDate, $endDate);
        $topProducts = $this->getTopProducts($startDate, $endDate);
        $topSellers = $this->getTopSellers($startDate, $endDate);

        $storageBalance = $this->getStorageBalance($storageId);
        $expensesType = $this->getExpensesType($startDate, $endDate);
        $monthlyStats = $this->getMonthlyStats($startDate, $endDate);


        if (Yii::$app->request->isAjax) {
            return $this->asJson([
                    'salesData' => $salesData,
                    'topProducts' => $topProducts,
                    'topSellers' => $topSellers,
                    'storageBalance' => $storageBalance,
                    'expensesType' => $expensesType,
                'monthlyStats' => $monthlyStats
            ]);
        }

        // Определяем тип отображения на основе диапазона дат
        $startDateObj = new \DateTime($startDate);
        $endDateObj = new \DateTime($endDate);
        $diffDays = $endDateObj->diff($startDateObj)->days;
        $viewType = $diffDays > 1 ? 'monthly' : 'daily';

        $viewData = [
            'storageId' => $storageId,
            'salesData' => $salesData,
            'topProducts' => $topProducts,
            'topSellers' => $topSellers,
            'storageBalance' => $storageBalance,
            'expensesType' => $expensesType,
            'monthlyStats' => $monthlyStats,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'viewType' => $viewType
        ];


        return $this->render('index', $viewData);
    }

    public function actionGetSalesData()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Only AJAX requests are allowed'];
        }

        $startDate = Yii::$app->request->post('start_date') . ' 00:00:00';
        $endDate = Yii::$app->request->post('end_date') . ' 23:59:59';

        try {
            $result = $this->getSalesData($startDate, $endDate);
            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function actionGetTopProducts()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Only AJAX requests are allowed'];
        }

        $startDate = Yii::$app->request->post('start_date') . ' 00:00:00';
        $endDate = Yii::$app->request->post('end_date') . ' 23:59:59';

        try {
            $result = $this->getTopProducts($startDate, $endDate);
            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function actionGetTopSellers()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Only AJAX requests are allowed'];
        }

        $startDate = Yii::$app->request->post('start_date') . ' 00:00:00';
        $endDate = Yii::$app->request->post('end_date') . ' 23:59:59';

        try {
            $result = $this->getTopSellers($startDate, $endDate);
            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function actionGetStorageBalance()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Only AJAX requests are allowed'];
        }

        $storageId = Yii::$app->request->post('storage_id');

        try {
            $result = $this->getStorageBalance($storageId);
            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function actionGetExpensesType()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Only AJAX requests are allowed'];
        }

        $startDate = Yii::$app->request->post('start_date') . ' 00:00:00';
        $endDate = Yii::$app->request->post('end_date') . ' 23:59:59';

        try {
            $result = $this->getExpensesType($startDate, $endDate);
            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }



    private function getExpensesType($startDate, $endDate)
    {
        if (strlen($startDate) <= 10) {
            $startDate .= ' 00:00:00';
        }
        if (strlen($endDate) <= 10) {
            $endDate .= ' 23:59:59';
        }

        $sql = "
            WITH ExpensesByType AS (
                SELECT
                    e.id,
                    e.name as expense_name,
                    eh.currency_id,
                    c.name as currency,
                    COUNT(eh.id) as operation_count,
                    COALESCE(SUM(eh.sum), 0) as total_amount
                FROM expenses e
                LEFT JOIN expense_history eh ON e.id = eh.expense_id
                LEFT JOIN currency c ON eh.currency_id = c.id
                WHERE
                    e.deleted_at IS NULL
                    AND eh.deleted_at IS NULL
                    AND eh.created_at BETWEEN :start_date AND :end_date
                GROUP BY e.id, e.name, eh.currency_id, c.name
            )
            SELECT
                id,
                expense_name,
                SUM(operation_count) as total_count,
                jsonb_object_agg(
                    COALESCE(currency, 'Неизвестно'),
                    total_amount
                ) as amounts_by_currency
            FROM ExpensesByType
            GROUP BY id, expense_name
            ORDER BY SUM(total_amount) DESC";

        $result = Yii::$app->db->createCommand($sql)
            ->bindValue(':start_date', $startDate)
            ->bindValue(':end_date', $endDate)
            ->queryAll();


        foreach ($result as &$row) {
            $sumWithCurrency = json_decode($row['amounts_by_currency']);
            foreach ($sumWithCurrency as $currency => $amount) {
                if($currency == 'Dollar'){
                    $currencyCourse = CurrencyCourse::find()
                        ->where(['currency_id' => 2])
                        ->andWhere(['<=', 'start_date', date('Y-m-d')])
                        ->andWhere(['>', 'end_date', date('Y-m-d')])
                        ->andWhere(['deleted_at' => null])
                        ->orderBy(['start_date' => SORT_DESC])
                        ->one();
                    $row['dollar_in_sum'] = $amount * $currencyCourse->course;
                }
            }
        }


        return $result;
    }



    private function getSalesData($startDate, $endDate)
    {
        // Определяем тип группировки на основе диапазона дат
        $startDateObj = new \DateTime($startDate);
        $endDateObj = new \DateTime($endDate);
        $diffDays = $endDateObj->diff($startDateObj)->days;

        // Исправленная логика группировки: если разница больше 7 дней, группируем по месяцам
        $groupBy = $diffDays > 7
            ? "TO_CHAR(o.accepted_date, 'YYYY-MM')"
            : "TO_CHAR(o.accepted_date, 'YYYY-MM-DD')";

        // Добавляем контроль доступа как в TrackingController
        $user = Yii::$app->user->identity;
        $isAdmin = $user->type == \app\common\models\User::TYPE_ADMIN;

        $storageFilter = '';
        // Добавляем фильтр по складу для не-админов
        if (!$isAdmin) {
            $storageFilter = 'AND o.storage_id = ' . (int)$user->storage->id;
        }

        $salesSql = "
            WITH order_totals AS (
                -- Основные заказы с агрегированными данными по заказу
                SELECT
                    o.id,
                    {$groupBy} AS date_group,
                    o.total_sell_price,
                    o.benefit,
                    SUM(od.size_meter) as total_size_meter
                FROM orders o
                JOIN order_detail od ON od.order_id = o.id
                WHERE
                    o.deleted_at IS NULL
                    AND od.deleted_at IS NULL
                    AND o.accepted_date BETWEEN :start_date AND :end_date
                    AND o.status IN (:status1, :status2, :status3)
                    {$storageFilter}
                GROUP BY o.id, date_group, o.total_sell_price, o.benefit
            ),
            -- ИСПРАВЛЕНО: Возвраты как отдельные записи с отрицательными значениями по дате возврата
            return_records AS (
                SELECT
                    " . ($diffDays > 7
                        ? "TO_CHAR(r.created_at, 'YYYY-MM')"
                        : "TO_CHAR(r.created_at, 'YYYY-MM-DD')") . " AS date_group,
                    -SUM(r.return_amount) as return_amount,
                    -SUM(COALESCE(r.original_benefit, 0)) as return_benefit,
                    -SUM(r.size_meter) as return_size_meter
                FROM order_returns r
                JOIN orders o ON o.id = r.order_id
                WHERE
                    r.status = 1
                    AND r.created_at BETWEEN :start_date AND :end_date
                    " . str_replace('o.storage_id', 'o.storage_id', $storageFilter) . "
                GROUP BY date_group
            ),
            all_periods AS (
                -- Объединяем все периоды для полного покрытия
                SELECT date_group FROM order_totals
                UNION
                SELECT date_group FROM return_records
            )
            SELECT
                ap.date_group,
                COALESCE(COUNT(DISTINCT ot.id), 0) AS total_orders,
                -- ИСПРАВЛЕНО: Используем MAX для возвратов, чтобы избежать умножения на количество заказов
                COALESCE(SUM(ot.total_sell_price), 0) + COALESCE(MAX(rr.return_amount), 0) AS total_sales,
                COALESCE(SUM(ot.total_sell_price), 0) AS total_sell_price,
                COALESCE(SUM(ot.benefit), 0) + COALESCE(MAX(rr.return_benefit), 0) AS total_benefit,
                COALESCE(SUM(ot.total_size_meter), 0) + COALESCE(MAX(rr.return_size_meter), 0) AS total_meters_sold,
                -- Добавляем отдельные поля для возвратов для анализа (как положительные значения)
                COALESCE(-MAX(rr.return_amount), 0) AS total_returns_amount,
                COALESCE(-MAX(rr.return_size_meter), 0) AS total_returns_meters
            FROM all_periods ap
            LEFT JOIN order_totals ot ON ot.date_group = ap.date_group
            LEFT JOIN return_records rr ON rr.date_group = ap.date_group
            GROUP BY ap.date_group
            ORDER BY ap.date_group ASC";

        $result = Yii::$app->db->createCommand($salesSql)
            ->bindValue(':start_date', $startDate)
            ->bindValue(':end_date', $endDate)
            ->bindValue(':status1', Orders::STATUS_ACCEPTED)
            ->bindValue(':status2', Orders::STATUS_COMPLETED)
            ->bindValue(':status3', Orders::STATUS_RETURNED)
            ->queryAll();

        // Если результат пустой, генерируем пустые периоды для стабильного отображения графика
        if (empty($result)) {
            return $this->generateEmptyPeriods($startDate, $endDate, $diffDays);
        }

        // Получаем курс доллара для конвертации
        $currencyCourse = CurrencyCourse::find()
            ->where(['currency_id' => 2]) // ID доллара
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere(['>', 'end_date', date('Y-m-d')])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        $dollarRate = ($currencyCourse && $currencyCourse->course > 0) ? $currencyCourse->course : 12600; // Fallback курс

        // Обеспечиваем корректные типы данных и конвертируем в доллары
        foreach ($result as &$row) {
            $row['total_orders'] = (int)($row['total_orders'] ?? 0);

            // Конвертируем суммы из сумов в доллары
            $row['total_sales'] = round((float)($row['total_sales'] ?? 0) / $dollarRate, 2);
            $row['total_sell_price'] = round((float)($row['total_sell_price'] ?? 0) / $dollarRate, 2);
            $row['total_returns_amount'] = round((float)($row['total_returns_amount'] ?? 0) / $dollarRate, 2);

            // Эти поля остаются без изменений
            $row['total_benefit'] = round((float)($row['total_benefit'] ?? 0), 2);
            $row['total_meters_sold'] = round((float)($row['total_meters_sold'] ?? 0), 2);
            $row['total_returns_meters'] = round((float)($row['total_returns_meters'] ?? 0), 2);

            // Для совместимости с существующим кодом добавляем total_price
            $row['total_price'] = $row['total_sales'];
        }

        return $result;
    }

    /**
     * Генерирует пустые периоды для корректного отображения графика
     */
    private function generateEmptyPeriods($startDate, $endDate, $diffDays)
    {
        $periods = [];
        $current = new \DateTime($startDate);
        $end = new \DateTime($endDate);

        // Определяем интервал на основе количества дней
        $interval = $diffDays > 7 ? 'P1M' : 'P1D';
        $format = $diffDays > 7 ? 'Y-m' : 'Y-m-d';

        while ($current <= $end) {
            $periods[] = [
                'date_group' => $current->format($format),
                'total_orders' => 0,
                'total_sales' => 0,
                'total_price' => 0,
                'total_sell_price' => 0,
                'total_benefit' => 0,
                'total_meters_sold' => 0,
                'total_returns_amount' => 0,
                'total_returns_meters' => 0
            ];

            $current->add(new \DateInterval($interval));

            // Для месячной группировки переходим к первому числу следующего месяца
            if ($interval === 'P1M') {
                $current->modify('first day of this month');
            }
        }

        return $periods;
    }

    private function getTopProducts($startDate, $endDate)
    {
        // Определяем тип группировки на основе диапазона дат
        $startDateObj = new \DateTime($startDate);
        $endDateObj = new \DateTime($endDate);
        $diffDays = $endDateObj->diff($startDateObj)->days;

        // Исправленная логика группировки: если разница больше 7 дней, группируем по месяцам
        $groupBy = $diffDays > 7
            ? "TO_CHAR(o.accepted_date, 'YYYY-MM')"
            : "TO_CHAR(o.accepted_date, 'YYYY-MM-DD')";

        $topBySalesSql = "
            WITH product_sales AS (
                -- Продажи продуктов с агрегированными данными
                SELECT
                    p.id,
                    p.name as product_name,
                    {$groupBy} as date_group,
                    SUM(od.sell_price) as total_sales_amount,
                    SUM(od.size_meter) as total_meters_sold,
                    COUNT(DISTINCT od.id) as total_sales_count
                FROM order_detail od
                JOIN products p ON p.id = od.product_id
                JOIN orders o ON o.id = od.order_id
                WHERE
                    o.deleted_at IS NULL
                    AND od.deleted_at IS NULL
                    AND o.accepted_date BETWEEN :start_date AND :end_date
                    AND o.status IN (:status1, :status2, :status3)
                GROUP BY p.id, p.name, date_group
            ),
            product_returns AS (
                -- Возвраты продуктов за период
                SELECT
                    p.id as product_id,
                    {$groupBy} as date_group,
                    SUM(r.return_amount) as total_return_amount,
                    SUM(r.size_meter) as total_return_meters,
                    COUNT(DISTINCT r.id) as total_return_count
                FROM order_returns r
                JOIN order_detail od ON r.order_detail_id = od.id
                JOIN products p ON p.id = od.product_id
                JOIN orders o ON o.id = r.order_id
                WHERE
                    r.status = 1
                    AND r.created_at BETWEEN :start_date AND :end_date
                GROUP BY p.id, date_group
            ),
            RankedProducts AS (
                SELECT
                    ps.id,
                    ps.product_name,
                    ps.date_group,
                    COALESCE(ps.total_sales_count, 0) - COALESCE(pr.total_return_count, 0) as total_sales_count,
                    COALESCE(ps.total_sales_amount, 0) - COALESCE(pr.total_return_amount, 0) as total_sales_amount,
                    COALESCE(ps.total_meters_sold, 0) - COALESCE(pr.total_return_meters, 0) as total_meters_sold,
                    ROW_NUMBER() OVER (PARTITION BY ps.date_group ORDER BY
                        (COALESCE(ps.total_sales_amount, 0) - COALESCE(pr.total_return_amount, 0)) DESC) as sales_rank
                FROM product_sales ps
                LEFT JOIN product_returns pr ON pr.product_id = ps.id AND pr.date_group = ps.date_group
                WHERE (COALESCE(ps.total_sales_amount, 0) - COALESCE(pr.total_return_amount, 0)) > 0
            )
            SELECT *
            FROM RankedProducts
            WHERE sales_rank <= 10
            ORDER BY total_sales_amount DESC";

        $topByQuantitySql = "
            WITH product_sales AS (
                -- Продажи продуктов с агрегированными данными
                SELECT
                    p.id,
                    p.name as product_name,
                    {$groupBy} as date_group,
                    SUM(od.sell_price) as total_sales_amount,
                    SUM(od.size_meter) as total_meters_sold,
                    COUNT(DISTINCT od.id) as total_sales_count
                FROM order_detail od
                JOIN products p ON p.id = od.product_id
                JOIN orders o ON o.id = od.order_id
                WHERE
                    o.deleted_at IS NULL
                    AND od.deleted_at IS NULL
                    AND o.accepted_date BETWEEN :start_date AND :end_date
                    AND o.status IN (:status1, :status2, :status3)
                GROUP BY p.id, p.name, date_group
            ),
            product_returns AS (
                -- Возвраты продуктов за период
                SELECT
                    p.id as product_id,
                    {$groupBy} as date_group,
                    SUM(r.return_amount) as total_return_amount,
                    SUM(r.size_meter) as total_return_meters,
                    COUNT(DISTINCT r.id) as total_return_count
                FROM order_returns r
                JOIN order_detail od ON r.order_detail_id = od.id
                JOIN products p ON p.id = od.product_id
                JOIN orders o ON o.id = r.order_id
                WHERE
                    r.status = 1
                    AND r.created_at BETWEEN :start_date AND :end_date
                GROUP BY p.id, date_group
            ),
            RankedProducts AS (
                SELECT
                    ps.id,
                    ps.product_name,
                    ps.date_group,
                    COALESCE(ps.total_sales_count, 0) - COALESCE(pr.total_return_count, 0) as total_sales_count,
                    COALESCE(ps.total_sales_amount, 0) - COALESCE(pr.total_return_amount, 0) as total_sales_amount,
                    COALESCE(ps.total_meters_sold, 0) - COALESCE(pr.total_return_meters, 0) as total_meters_sold,
                    ROW_NUMBER() OVER (PARTITION BY ps.date_group ORDER BY
                        (COALESCE(ps.total_meters_sold, 0) - COALESCE(pr.total_return_meters, 0)) DESC) as quantity_rank
                FROM product_sales ps
                LEFT JOIN product_returns pr ON pr.product_id = ps.id AND pr.date_group = ps.date_group
                WHERE (COALESCE(ps.total_meters_sold, 0) - COALESCE(pr.total_return_meters, 0)) > 0
            )
            SELECT *
            FROM RankedProducts
            WHERE quantity_rank <= 10
            ORDER BY total_meters_sold DESC";

        $bySales = Yii::$app->db->createCommand($topBySalesSql)
            ->bindValue(':start_date', $startDate)
            ->bindValue(':end_date', $endDate)
            ->bindValue(':status1', Orders::STATUS_ACCEPTED)
            ->bindValue(':status2', Orders::STATUS_COMPLETED)
            ->bindValue(':status3', Orders::STATUS_RETURNED)
            ->queryAll();

        $byQuantity = Yii::$app->db->createCommand($topByQuantitySql)
            ->bindValue(':start_date', $startDate)
            ->bindValue(':end_date', $endDate)
            ->bindValue(':status1', Orders::STATUS_ACCEPTED)
            ->bindValue(':status2', Orders::STATUS_COMPLETED)
            ->bindValue(':status3', Orders::STATUS_RETURNED)
            ->queryAll();

        // Получаем курс доллара для конвертации
        $currencyCourse = CurrencyCourse::find()
            ->where(['currency_id' => 2]) // ID доллара
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere(['>', 'end_date', date('Y-m-d')])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        $dollarRate = ($currencyCourse && $currencyCourse->course > 0) ? $currencyCourse->course : 12600; // Fallback курс

        // НЕ конвертируем суммы для продуктов, так как sell_price в order_detail уже в долларах
        // total_sales_amount уже в долларах из order_detail.sell_price

        // Отладочный вывод
        Yii::info('TopProducts byQuantity data: ' . json_encode($byQuantity), 'dashboard');

        return [
            'bySales' => $bySales,      // Суммы в долларах
            'byQuantity' => $byQuantity // Суммы в долларах, метры остаются без изменений
        ];
    }



    private function getTopSellers($startDate, $endDate)
    {
        // Определяем тип группировки на основе диапазона дат
        $startDateObj = new \DateTime($startDate);
        $endDateObj = new \DateTime($endDate);
        $diffDays = $endDateObj->diff($startDateObj)->days;

        // Исправленная логика группировки: если разница больше 7 дней, группируем по месяцам
        $groupBy = $diffDays > 7
            ? "TO_CHAR(o.accepted_date, 'YYYY-MM')"
            : "TO_CHAR(o.accepted_date, 'YYYY-MM-DD')";

        $topBySalesSql = "
            WITH seller_sales AS (
                -- Продажи продавцов с агрегированными данными
                SELECT
                    u.id,
                    u.username as seller_name,
                    {$groupBy} as date_group,
                    SUM(o.total_sell_price) as total_sales_amount,
                    SUM(o.benefit) as total_benefit,
                    COUNT(DISTINCT o.id) as total_sales_count
                FROM orders o
                JOIN users u ON u.id = o.user_id
                WHERE
                    o.deleted_at IS NULL
                    AND o.accepted_date BETWEEN :start_date AND :end_date
                    AND o.status IN (:status1, :status2, :status3)
                GROUP BY u.id, u.username, date_group
            ),
            seller_returns AS (
                -- Возвраты продавцов за период
                SELECT
                    u.id as seller_id,
                    {$groupBy} as date_group,
                    SUM(r.return_amount) as total_return_amount,
                    SUM(COALESCE(r.original_benefit, 0)) as total_return_benefit,
                    COUNT(DISTINCT r.order_id) as total_return_count
                FROM order_returns r
                JOIN orders o ON o.id = r.order_id
                JOIN users u ON u.id = o.user_id
                WHERE
                    r.status = 1
                    AND r.created_at BETWEEN :start_date AND :end_date
                GROUP BY u.id, date_group
            ),
            RankedSellers AS (
                SELECT
                    ss.id,
                    ss.seller_name,
                    ss.date_group,
                    COALESCE(ss.total_sales_count, 0) - COALESCE(sr.total_return_count, 0) as total_sales_count,
                    COALESCE(ss.total_sales_amount, 0) - COALESCE(sr.total_return_amount, 0) as total_sales_amount,
                    COALESCE(ss.total_benefit, 0) - COALESCE(sr.total_return_benefit, 0) as total_benefit,
                    ROW_NUMBER() OVER (PARTITION BY ss.date_group ORDER BY
                        (COALESCE(ss.total_sales_amount, 0) - COALESCE(sr.total_return_amount, 0)) DESC) as sales_rank
                FROM seller_sales ss
                LEFT JOIN seller_returns sr ON sr.seller_id = ss.id AND sr.date_group = ss.date_group
                WHERE (COALESCE(ss.total_sales_amount, 0) - COALESCE(sr.total_return_amount, 0)) > 0
            )
            SELECT *
            FROM RankedSellers
            WHERE sales_rank <= 10
            ORDER BY total_sales_amount DESC";

        $topByQuantitySql = "
            WITH seller_sales AS (
                -- Продажи продавцов с агрегированными данными по метрам
                SELECT
                    u.id,
                    u.username as seller_name,
                    {$groupBy} as date_group,
                    SUM(od.size_meter) as total_meters_sold,
                    COUNT(DISTINCT o.id) as total_sales_count
                FROM orders o
                JOIN users u ON u.id = o.user_id
                JOIN order_detail od ON od.order_id = o.id
                WHERE
                    o.deleted_at IS NULL
                    AND od.deleted_at IS NULL
                    AND o.accepted_date BETWEEN :start_date AND :end_date
                    AND o.status IN (:status1, :status2, :status3)
                GROUP BY u.id, u.username, date_group
            ),
            seller_returns AS (
                -- Возвраты продавцов за период по метрам
                SELECT
                    u.id as seller_id,
                    {$groupBy} as date_group,
                    SUM(r.size_meter) as total_return_meters,
                    COUNT(DISTINCT r.order_id) as total_return_count
                FROM order_returns r
                JOIN orders o ON o.id = r.order_id
                JOIN users u ON u.id = o.user_id
                WHERE
                    r.status = 1
                    AND r.created_at BETWEEN :start_date AND :end_date
                GROUP BY u.id, date_group
            ),
            RankedSellers AS (
                SELECT
                    ss.id,
                    ss.seller_name,
                    ss.date_group,
                    COALESCE(ss.total_sales_count, 0) - COALESCE(sr.total_return_count, 0) as total_sales_count,
                    COALESCE(ss.total_meters_sold, 0) - COALESCE(sr.total_return_meters, 0) as total_meters_sold,
                    ROW_NUMBER() OVER (PARTITION BY ss.date_group ORDER BY
                        (COALESCE(ss.total_meters_sold, 0) - COALESCE(sr.total_return_meters, 0)) DESC) as quantity_rank
                FROM seller_sales ss
                LEFT JOIN seller_returns sr ON sr.seller_id = ss.id AND sr.date_group = ss.date_group
                WHERE (COALESCE(ss.total_meters_sold, 0) - COALESCE(sr.total_return_meters, 0)) > 0
            )
            SELECT *
            FROM RankedSellers
            WHERE quantity_rank <= 10
            ORDER BY total_meters_sold DESC";

        $bySales = Yii::$app->db->createCommand($topBySalesSql)
            ->bindValue(':start_date', $startDate)
            ->bindValue(':end_date', $endDate)
            ->bindValue(':status1', Orders::STATUS_ACCEPTED)
            ->bindValue(':status2', Orders::STATUS_COMPLETED)
            ->bindValue(':status3', Orders::STATUS_RETURNED)
            ->queryAll();

        $byQuantity = Yii::$app->db->createCommand($topByQuantitySql)
            ->bindValue(':start_date', $startDate)
            ->bindValue(':end_date', $endDate)
            ->bindValue(':status1', Orders::STATUS_ACCEPTED)
            ->bindValue(':status2', Orders::STATUS_COMPLETED)
            ->bindValue(':status3', Orders::STATUS_RETURNED)
            ->queryAll();

        // Получаем курс доллара для конвертации
        $currencyCourse = CurrencyCourse::find()
            ->where(['currency_id' => 2]) // ID доллара
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere(['>', 'end_date', date('Y-m-d')])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        $dollarRate = ($currencyCourse && $currencyCourse->course > 0) ? $currencyCourse->course : 12600; // Fallback курс

        // Конвертируем суммы в доллары для bySales
        foreach ($bySales as &$seller) {
            $seller['total_sales_amount'] = round((float)($seller['total_sales_amount'] ?? 0) / $dollarRate, 2);
            $seller['total_benefit'] = round((float)($seller['total_benefit'] ?? 0), 2); // Benefit остается без изменений
        }

        // Отладочный вывод
        Yii::info('TopSellers byQuantity data: ' . json_encode($byQuantity), 'dashboard');

        return [
            'bySales' => $bySales,      // Суммы в долларах
            'byQuantity' => $byQuantity // Метры остаются без изменений
        ];
    }

    private function getStorageBalance($storageId = null)
    {
        $sql = "
            WITH LatestHistory AS (
                SELECT
                    sbh.product_serial_id,
                    sbh.storage_id,
                    sbh.size,
                    ROW_NUMBER() OVER (PARTITION BY sbh.product_serial_id ORDER BY sbh.created_at DESC) as rn
                FROM storage_balance_history sbh
                WHERE sbh.deleted_at IS NULL
                    AND sbh.size > 0
                    " . ($storageId ? "AND sbh.storage_id = :storage_id" : "") . "
            )
            SELECT
                p.id as product_id,
                p.name as product_name,
                COALESCE(SUM(lh.size), 0) as total_remainder
            FROM products p
            LEFT JOIN storage_balance sb ON sb.product_id = p.id
            LEFT JOIN product_serial ps ON ps.storage_balance_id = sb.id
            LEFT JOIN LatestHistory lh ON lh.product_serial_id = ps.id AND lh.rn = 1
            WHERE p.deleted_at IS NULL
            GROUP BY p.id, p.name
            HAVING COALESCE(SUM(lh.size), 0) > 0
            ORDER BY total_remainder ASC
            limit 12";

        $command = Yii::$app->db->createCommand($sql);
        if ($storageId) {
            $command->bindValue(':storage_id', $storageId);
        }

        return $command->queryAll();
    }

    public function actionGetMonthlyStats()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Only AJAX requests are allowed'];
        }

        $startDate = Yii::$app->request->post('start_date') . ' 00:00:00';
        $endDate = Yii::$app->request->post('end_date') . ' 23:59:59';

        try {
            $monthlyStats = $this->getMonthlyStats($startDate, $endDate);
            return [
                'success' => true,
                'data' => $monthlyStats
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    private function getMonthlyStats($startDate, $endDate)
    {
        if (strlen($startDate) <= 10) {
            $startDate .= ' 00:00:00';
        }
        if (strlen($endDate) <= 10) {
            $endDate .= ' 23:59:59';
        }

        $salesData = $this->getSalesData($startDate, $endDate);
        $expensesData = $this->getExpensesByMonth($startDate, $endDate);

        // Получаем курс доллара один раз для всех расчетов
        $currencyCourse = CurrencyCourse::find()
            ->where(['currency_id' => 2]) // ID доллара
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere(['>', 'end_date', date('Y-m-d')])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        $dollarRate = ($currencyCourse && $currencyCourse->course > 0) ? $currencyCourse->course : 12600; // Fallback курс

        // Создаем ассоциативные массивы для быстрого доступа
        $salesByMonth = [];
        $expensesByMonth = [];
        $allMonths = [];

        // Обрабатываем данные продаж (агрегируем по месяцам)
        foreach ($salesData as $sale) {
            $month = date('Y-m', strtotime($sale['date_group']));

            // Суммируем продажи по месяцам (total_price уже в долларах)
            if (!isset($salesByMonth[$month])) {
                $salesByMonth[$month] = 0;
            }
            $salesByMonth[$month] += floatval($sale['total_price']);
            $allMonths[$month] = true;
        }

        // Обрабатываем данные расходов (агрегируем по месяцам и конвертируем в доллары)
        foreach ($expensesData as $expense) {
            $month = $expense['date_group'];

            // Суммируем расходы по месяцам и конвертируем в доллары
            if (!isset($expensesByMonth[$month])) {
                $expensesByMonth[$month] = 0;
            }
            $expensesByMonth[$month] += round(floatval($expense['total_in_sum']) / $dollarRate, 2);
            $allMonths[$month] = true;
        }

        // Получаем отсортированный список месяцев
        $months = array_keys($allMonths);
        sort($months);

        // Формируем итоговые массивы (все значения в долларах)
        $sales = [];
        $expenses = [];
        foreach ($months as $month) {
            $sales[] = isset($salesByMonth[$month]) ? $salesByMonth[$month] : 0;
            $expenses[] = isset($expensesByMonth[$month]) ? $expensesByMonth[$month] : 0;
        }

        return [
            'months' => $months,
            'sales' => $sales,      // В долларах
            'expenses' => $expenses // В долларах
        ];
    }


    private function getExpensesByMonth($startDate, $endDate)
    {
        if (strlen($startDate) <= 10) {
            $startDate .= ' 00:00:00';
        }
        if (strlen($endDate) <= 10) {
            $endDate .= ' 23:59:59';
        }

        $sql = "
            WITH ExpensesByMonth AS (
                SELECT
                    TO_CHAR(eh.created_at, 'YYYY-MM') as date_group,
                    eh.currency_id,
                    c.name as currency,
                    COALESCE(SUM(eh.sum), 0) as total_amount
                FROM expense_history eh
                LEFT JOIN currency c ON eh.currency_id = c.id
                WHERE
                    eh.deleted_at IS NULL
                    AND eh.created_at BETWEEN :start_date AND :end_date
                GROUP BY date_group, eh.currency_id, c.name
            )
            SELECT
                date_group,
                jsonb_object_agg(
                    COALESCE(currency, 'Неизвестно'),
                    total_amount
                ) as amounts_by_currency
            FROM ExpensesByMonth
            GROUP BY date_group
            ORDER BY date_group ASC";

        $result = Yii::$app->db->createCommand($sql)
            ->bindValue(':start_date', $startDate)
            ->bindValue(':end_date', $endDate)
            ->queryAll();

        // Конвертируем доллары в сумы
        foreach ($result as &$row) {
            $sumWithCurrency = json_decode($row['amounts_by_currency'], true);
            $totalInSum = 0;

            foreach ($sumWithCurrency as $currency => $amount) {
                if ($currency == 'Dollar') {
                    $currencyCourse = CurrencyCourse::find()
                        ->where(['currency_id' => 2])
                        ->andWhere(['<=', 'start_date', date('Y-m-d')])
                        ->andWhere(['>', 'end_date', date('Y-m-d')])
                        ->andWhere(['deleted_at' => null])
                        ->orderBy(['start_date' => SORT_DESC])
                        ->one();
                    $totalInSum += $amount * $currencyCourse->course;
                } else {
                    $totalInSum += $amount;
                }
            }
            $row['total_in_sum'] = $totalInSum;
        }

        return $result;
    }
}