<?php

use yii\bootstrap5\Html;
use yii\web\View;
use yii\widgets\Pjax;
use app\common\models\SuplierBalanceHistory;
use app\common\models\CashboxBalanceHistory;

$this->title = Yii::t("app", "payment_history");
$this->params['breadcrumbs'][] = ['label' => Yii::t("app", "suplier_section"), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

// Функция для конвертации валют в доллары
function convertToDollars($amount, $currencyName) {
    if (strtolower($currencyName) === 'dollar' || strtolower($currencyName) === 'usd') {
        return $amount;
    }

    if (strtolower($currencyName) === 'sum' || strtolower($currencyName) === 'uzs') {
        return $amount / 12500; // Примерный курс
    }

    return $amount;
}

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

$labelsJson = json_encode([
    'search' => $searchLabel,
    'lengthMenu' => $lengthMenuLabel,
    'zeroRecords' => $zeroRecordsLabel,
    'info' => $infoLabel,
    'infoEmpty' => $infoEmptyLabel,
    'infoFiltered' => $infoFilteredLabel,
]);

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-8">
            <h4 class="my-0">
                <i class="fa fa-credit-card text-primary"></i>
                <?= Html::encode($this->title) ?>
            </h4>
        </div>
        <div class="col-md-4 text-right">
            <?= Html::a('</i> ' . Yii::t('app', 'back_to_suppliers'), ['index'], ['class' => 'btn btn-primary btn-sm rounded-pill']) ?>
        </div>
    </div>

   

    <?php Pjax::begin(['id' => 'payment-history-pjax']); ?>


        <!-- История баланса поставщиков -->
            <?php if (!empty($paymentHistory)): ?>
                <div class="table-responsive">
                    <table id="supplier-balance-table" class="table table-hover table-striped">
                        <thead class="thead-light">
                        <tr>
                            <th class="text-center"><i class="fa fa-calendar"></i> <?= Yii::t('app', 'payment_date') ?></th>
                            <th class="text-center"><i class="fa fa-dollar-sign"></i> <?= Yii::t('app', 'amount') ?></th>
                            <th class="text-center"><i class="fa fa-money-bill"></i> <?= Yii::t('app', 'currency') ?></th>
                            <th class="text-center"><i class="fa fa-tag"></i> <?= Yii::t('app', 'Type') ?></th>
                            <th class="text-center"><i class="fa fa-user-tie"></i> <?= Yii::t('app', 'user') ?></th>
                            <th class="text-center"><i class="fa fa-comment"></i> <?= Yii::t('app', 'description') ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($paymentHistory as $payment): ?>
                            <tr>
                                <td class="text-center">
                                <span class="badge badge-light">
                                    <?= date('d.m.Y H:i', strtotime($payment->created_at)) ?>
                                </span>

                                </td>
                               
                                <td class="text-right font-weight-bold">
                                    <span class="text-<?= $payment->sum < 0 ? 'danger' : 'success' ?>">
                                        <?= number_format($payment->sum, 2) ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-secondary">
                                        <?= Html::encode($payment->currency ? $payment->currency->name : '') ?>
                                    </span>
                                </td>
                              
                                <td class="text-center">
                                    <?php
                                    $typeClass = '';
                                    switch ($payment->type) {
                                        case SuplierBalanceHistory::TYPE_INCOME:
                                            $typeClass = 'success';
                                            break;
                                        case SuplierBalanceHistory::TYPE_EXPENSE:
                                            $typeClass = 'danger';
                                            break;
                                        case SuplierBalanceHistory::TYPE_RETURN:
                                            $typeClass = 'warning';
                                            break;
                                        default:
                                            $typeClass = 'secondary';
                                    }
                                    ?>
                                    <span class="badge badge-<?= $typeClass ?>">
                                        <?= $payment->getTypeName() ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="text-muted">
                                        <?= Html::encode($payment->user ? $payment->user->full_name : 'N/') ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="text-muted">
                                        <?= Html::encode($payment->description ?: '-') ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fa fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?= Yii::t('app', 'No data available.') ?></h5>
                </div>
            <?php endif; ?>
        </div>

        <!-- История кассы (платежи поставщикам) -->
        <div class="tab-pane fade p-4" id="cashbox-history" role="tabpanel">
            <?php if (!empty($cashboxHistory)): ?>
                <div class="table-responsive">
                    <table id="cashbox-history-table" class="table table-hover table-striped">
                        <thead class="thead-light">
                        <tr>
                            <th class="text-center"><i class="fa fa-calendar"></i> <?= Yii::t('app', 'Date') ?></th>
                            <th class="text-center"><i class="fa fa-cash-register"></i> <?= Yii::t('app', 'Cashbox') ?></th>
                            <th class="text-center"><i class="fa fa-dollar-sign"></i> <?= Yii::t('app', 'Amount') ?></th>
                            <th class="text-center"><i class="fa fa-money-bill"></i> <?= Yii::t('app', 'Currency') ?></th>
                            <th class="text-center"><i class="fa fa-exchange-alt"></i> <?= Yii::t('app', 'Amount (USD)') ?></th>
                            <th class="text-center"><i class="fa fa-tag"></i> <?= Yii::t('app', 'Type') ?></th>
                            <th class="text-center"><i class="fa fa-user-tie"></i> <?= Yii::t('app', 'User') ?></th>
                            <th class="text-center"><i class="fa fa-comment"></i> <?= Yii::t('app', 'Description') ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($cashboxHistory as $cashbox): ?>
                            <tr>
                                <td class="text-center">
                                    <span class="badge badge-light">
                                        <?= Yii::$app->formatter->asDatetime($cashbox->created_at, 'php:d.m.Y H:i') ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="font-weight-bold text-info">
                                        <?= Html::encode($cashbox->cashbox ? $cashbox->cashbox->name : 'N/A') ?>
                                    </span>
                                </td>
                                <td class="text-right font-weight-bold">
                                    <span class="text-<?= $cashbox->sum < 0 ? 'danger' : 'success' ?>">
                                        <?= number_format($cashbox->sum, 2) ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-secondary">
                                        <?= Html::encode($cashbox->currency ? $cashbox->currency->name : 'N/A') ?>
                                    </span>
                                </td>
                                <td class="text-right font-weight-bold">
                                    <span class="text-primary">
                                        $<?= number_format(convertToDollars($cashbox->sum, $cashbox->currency ? $cashbox->currency->name : ''), 2) ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <?php
                                    $typeClass = '';
                                    switch ($cashbox->type) {
                                        case CashboxBalanceHistory::TYPE_INCOME:
                                            $typeClass = 'success';
                                            break;
                                        case CashboxBalanceHistory::TYPE_EXPENSES:
                                            $typeClass = 'danger';
                                            break;
                                        case CashboxBalanceHistory::TYPE_RETURN:
                                            $typeClass = 'warning';
                                            break;
                                        default:
                                            $typeClass = 'secondary';
                                    }
                                    ?>
                                    <span class="badge badge-<?= $typeClass ?>">
                                        <?php
                                        switch ($cashbox->type) {
                                            case CashboxBalanceHistory::TYPE_INCOME:
                                                echo Yii::t('app', 'Income');
                                                break;
                                            case CashboxBalanceHistory::TYPE_EXPENSES:
                                                echo Yii::t('app', 'Expense');
                                                break;
                                            case CashboxBalanceHistory::TYPE_RETURN:
                                                echo Yii::t('app', 'Return');
                                                break;
                                            default:
                                                echo Yii::t('app', 'Unknown');
                                        }
                                        ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="text-muted">
                                        <?= Html::encode($cashbox->user ? $cashbox->user->full_name : 'N/A') ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="text-muted">
                                        <?= Html::encode($cashbox->description ?: '-') ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fa fa-cash-register fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?= Yii::t('app', 'No cashbox history available') ?></h5>
                    <p class="text-muted"><?= Yii::t('app', 'Cashbox transactions will appear here') ?></p>
                </div>
            <?php endif; ?>

    <?php Pjax::end(); ?>
</div>

<?php
$js = <<<JS
(function($) {
    const labels = {$labelsJson};
    let supplierTable, cashboxTable;

    function initDataTable(tableId) {
        return $(tableId).DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true,
            pageLength: 25,
            responsive: true,
            language: {
                paginate: {
                    previous: '<span class="custom-pagination prev"><i class="fa fa-arrow-left"></i></span>',
                    next: '<span class="custom-pagination next"><i class="fa fa-arrow-right"></i></span>'
                },
                search: labels.search,
                lengthMenu: labels.lengthMenu,
                zeroRecords: labels.zeroRecords,
                info: labels.info,
                infoEmpty: labels.infoEmpty,
                infoFiltered: labels.infoFiltered
            },
            columnDefs: [
                { targets: '_all', className: 'text-center' }
            ],
            order: [[ 0, 'desc' ]], // Сортировка по дате по убыванию
            drawCallback: function() {
                updateStatistics();
            }
        });
    }

    function initializeTables() {
        if ($.fn.DataTable.isDataTable('#supplier-balance-table')) {
            $('#supplier-balance-table').DataTable().destroy();
        }
        if ($.fn.DataTable.isDataTable('#cashbox-history-table')) {
            $('#cashbox-history-table').DataTable().destroy();
        }

        if ($('#supplier-balance-table').length) {
            supplierTable = initDataTable('#supplier-balance-table');
        }
        if ($('#cashbox-history-table').length) {
            cashboxTable = initDataTable('#cashbox-history-table');
        }
    }

    function updateStatistics() {
        let totalIncome = 0;
        let totalExpense = 0;
        let totalRecords = 0;

        // Подсчет статистики для активной таблицы
        const activeTable = $('.tab-pane.active table').DataTable();
        if (activeTable) {
            const visibleData = activeTable.rows({ search: 'applied' }).data();
            totalRecords = visibleData.length;

            visibleData.each(function(row) {
                // Извлекаем сумму в USD из соответствующей колонки
                const usdAmountText = $(row[4]).text() || '0'; // Колонка "Amount (USD)"
                const usdAmount = parseFloat(usdAmountText.replace(/[^0-9.-]/g, '')) || 0;

                if (usdAmount > 0) {
                    totalIncome += usdAmount;
                } else {
                    totalExpense += Math.abs(usdAmount);
                }
            });
        }

        const netBalance = totalIncome - totalExpense;

        $('#total-records').text(totalRecords);
        $('#total-income').text('$' + totalIncome.toFixed(2));
        $('#total-expense').text('$' + totalExpense.toFixed(2));
        $('#net-balance').text('$' + netBalance.toFixed(2));

        // Изменяем цвет net balance в зависимости от значения
        const netBalanceElement = $('#net-balance');
        netBalanceElement.removeClass('text-success text-danger text-info');
        if (netBalance > 0) {
            netBalanceElement.addClass('text-success');
        } else if (netBalance < 0) {
            netBalanceElement.addClass('text-danger');
        } else {
            netBalanceElement.addClass('text-info');
        }
    }

    function setupFilters() {
        // Фильтр по поставщику
        $('#supplier-filter').on('change', function() {
            const value = $(this).val();
            if (supplierTable) {
                supplierTable.column(1).search(value).draw();
            }
        });

        // Фильтр по валюте
        $('#currency-filter').on('change', function() {
            const value = $(this).val();
            const activeTable = $('.tab-pane.active table').DataTable();
            if (activeTable) {
                activeTable.column(3).search(value).draw();
            }
        });

        // Фильтр по пользователю
        $('#user-filter').on('change', function() {
            const value = $(this).val();
            const activeTable = $('.tab-pane.active table').DataTable();
            if (activeTable) {
                activeTable.column(6).search(value).draw();
            }
        });

        // Фильтр по типу операции
        $('#type-filter').on('change', function() {
            const value = $(this).val();
            const activeTable = $('.tab-pane.active table').DataTable();
            if (activeTable) {
                activeTable.column(5).search(value).draw();
            }
        });

        // Фильтр по дате
        $('#date-from, #date-to').on('change', function() {
            const dateFrom = $('#date-from').val();
            const dateTo = $('#date-to').val();

            $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                const dateStr = data[0]; // Первая колонка - дата
                if (!dateStr) return true;

                // Извлекаем дату из badge
                const dateMatch = dateStr.match(/(\d{2}\.\d{2}\.\d{4})/);
                if (!dateMatch) return true;

                const rowDate = new Date(dateMatch[1].split('.').reverse().join('-'));
                const fromDate = dateFrom ? new Date(dateFrom) : null;
                const toDate = dateTo ? new Date(dateTo) : null;

                if (fromDate && rowDate < fromDate) return false;
                if (toDate && rowDate > toDate) return false;

                return true;
            });

            const activeTable = $('.tab-pane.active table').DataTable();
            if (activeTable) {
                activeTable.draw();
            }
        });

        // Очистка фильтров
        $('#clear-filters').on('click', function() {
            $('#supplier-filter, #currency-filter, #user-filter, #type-filter').val('');
            $('#date-from, #date-to').val('');

            // Очищаем кастомные фильтры
            $.fn.dataTable.ext.search.pop();

            if (supplierTable) {
                supplierTable.search('').columns().search('').draw();
            }
            if (cashboxTable) {
                cashboxTable.search('').columns().search('').draw();
            }
        });
    }

    $(document).ready(function() {
        initializeTables();
        setupFilters();
        updateStatistics();

        // Обработка переключения табов
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            $.fn.dataTable.tables({visible: true, api: true}).columns.adjust();
            updateStatistics();
        });

        // Reinitialize on PJAX
        $(document).on('pjax:success', function() {
            initializeTables();
            setupFilters();
            updateStatistics();
        });
    });
})(jQuery);
JS;

$this->registerJs($js, View::POS_END);

// CSS стили
$css = <<<CSS
.nav-tabs-custom .nav-link {
    border-radius: 10px 10px 0 0;
    transition: all 0.3s ease;
}

.nav-tabs-custom .nav-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.nav-tabs-custom .nav-link.active {
    background-color: #007bff;
    color: white !important;
    border-color: #007bff;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transition: background-color 0.3s ease;
}

.badge {
    font-size: 0.85em;
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-sm.rounded-pill {
    padding: 0.375rem 1rem;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-pagination {
    padding: 0.375rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
}

.custom-pagination:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
    text-decoration: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #0056b3 !important;
    border-color: #0056b3 !important;
    color: white !important;
}
CSS;

$this->registerCss($css);
?>