<?php

use yii\bootstrap5\Html;
use yii\web\View;
use yii\widgets\Pjax;
use yii\widgets\LinkPager;
use app\common\models\SuplierBalanceHistory;
use app\common\models\CashboxBalanceHistory;

$this->title = Yii::t("app", "payment_history");
$this->params['breadcrumbs'][] = ['label' => Yii::t("app", "suplier_section"), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

// Функция для конвертации валют в доллары
function convertToDollars($amount, $currencyName) {
    if (strtolower($currencyName) === 'dollar' || strtolower($currencyName) === 'usd') {
        return $amount;
    }

    if (strtolower($currencyName) === 'sum' || strtolower($currencyName) === 'uzs') {
        return $amount / 12500; // Примерный курс
    }

    return $amount;
}

?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-8">
            <h4 class="my-0">
                <i class="fa fa-credit-card text-primary"></i>
                <?= Html::encode($this->title) ?>
            </h4>
            <p class="text-muted mb-0"><?= Yii::t('app', 'Complete history of all supplier payments') ?></p>
        </div>
        <div class="col-md-4 text-right">
            <?= Html::a('<i class="fa fa-arrow-left"></i> ' . Yii::t('app', 'Back to Suppliers'), ['index'], ['class' => 'btn btn-primary btn-sm rounded-pill']) ?>
        </div>
    </div>

    <!-- Фильтры -->
    <div class="card border-0 shadow-sm rounded-lg mb-4">
        <div class="card-header bg-light rounded-top">
            <h6 class="mb-0"><i class="fa fa-filter"></i> <?= Yii::t('app', 'Filters') ?></h6>
        </div>
        <div class="card-body">
            <?= Html::beginForm(['payment-history'], 'get', ['id' => 'filter-form']) ?>
            <?= Html::hiddenInput('tab', $activeTab) ?>
            <div class="row">
                <?php if ($activeTab === 'supplier-balance'): ?>
                <div class="col-md-3">
                    <label for="supplier-filter" class="form-label"><?= Yii::t('app', 'Supplier') ?></label>
                    <?= Html::dropDownList('supplier_filter', $filters['supplier_filter'],
                        \yii\helpers\ArrayHelper::map($suppliers, 'full_name', 'full_name'),
                        ['prompt' => Yii::t('app', 'All Suppliers'), 'class' => 'form-control']) ?>
                </div>
                <?php endif; ?>
                <div class="col-md-3">
                    <label for="currency-filter" class="form-label"><?= Yii::t('app', 'Currency') ?></label>
                    <?= Html::dropDownList('currency_filter', $filters['currency_filter'],
                        \yii\helpers\ArrayHelper::map($currencies, 'name', 'name'),
                        ['prompt' => Yii::t('app', 'All Currencies'), 'class' => 'form-control']) ?>
                </div>
                <div class="col-md-3">
                    <label for="user-filter" class="form-label"><?= Yii::t('app', 'User') ?></label>
                    <?= Html::dropDownList('user_filter', $filters['user_filter'],
                        \yii\helpers\ArrayHelper::map($users, 'full_name', 'full_name'),
                        ['prompt' => Yii::t('app', 'All Users'), 'class' => 'form-control']) ?>
                </div>
                <div class="col-md-3">
                    <label for="type-filter" class="form-label"><?= Yii::t('app', 'Operation Type') ?></label>
                    <?= Html::dropDownList('type_filter', $filters['type_filter'], [
                        'income' => Yii::t('app', 'Income'),
                        'expense' => Yii::t('app', 'Expense'),
                        'return' => Yii::t('app', 'Return')
                    ], ['prompt' => Yii::t('app', 'All Types'), 'class' => 'form-control']) ?>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <label for="date-from" class="form-label"><?= Yii::t('app', 'Date From') ?></label>
                    <?= Html::input('date', 'date_from', $filters['date_from'], ['class' => 'form-control']) ?>
                </div>
                <div class="col-md-4">
                    <label for="date-to" class="form-label"><?= Yii::t('app', 'Date To') ?></label>
                    <?= Html::input('date', 'date_to', $filters['date_to'], ['class' => 'form-control']) ?>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <?= Html::submitButton('<i class="fa fa-search"></i> ' . Yii::t('app', 'Filter'), ['class' => 'btn btn-info mr-2']) ?>
                    <?= Html::a('<i class="fa fa-times"></i> ' . Yii::t('app', 'Clear'), ['payment-history'], ['class' => 'btn btn-secondary']) ?>
                </div>
            </div>
            <?= Html::endForm() ?>
        </div>
    </div>

    <!-- Статистика -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm rounded-lg text-center">
                <div class="card-body">
                    <i class="fa fa-list-alt fa-2x text-primary mb-2"></i>
                    <h5 class="mb-0"><?= $dataProvider->totalCount ?></h5>
                    <small class="text-muted"><?= Yii::t('app', 'Total Records') ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm rounded-lg text-center">
                <div class="card-body">
                    <i class="fa fa-users fa-2x text-info mb-2"></i>
                    <h5 class="mb-0"><?= $totalPaymentRecords ?></h5>
                    <small class="text-muted"><?= Yii::t('app', 'Supplier Balance Records') ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm rounded-lg text-center">
                <div class="card-body">
                    <i class="fa fa-cash-register fa-2x text-success mb-2"></i>
                    <h5 class="mb-0"><?= $totalCashboxRecords ?></h5>
                    <small class="text-muted"><?= Yii::t('app', 'Cashbox Records') ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm rounded-lg text-center">
                <div class="card-body">
                    <i class="fa fa-chart-line fa-2x text-warning mb-2"></i>
                    <h5 class="mb-0"><?= $totalPaymentRecords + $totalCashboxRecords ?></h5>
                    <small class="text-muted"><?= Yii::t('app', 'Total All Records') ?></small>
                </div>
            </div>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'payment-history-pjax']); ?>

    <!-- Табы для разных типов истории -->
    <ul class="nav nav-tabs nav-tabs-custom rounded-top" id="historyTabs" role="tablist">
        <li class="nav-item">
            <?= Html::a('<i class="fa fa-users"></i> ' . Yii::t('app', 'Supplier Balance History') .
                ' <span class="badge badge-primary ml-2">' . $totalPaymentRecords . '</span>',
                ['payment-history', 'tab' => 'supplier-balance'] + $filters,
                ['class' => 'nav-link rounded-top ' . ($activeTab === 'supplier-balance' ? 'active' : '')]) ?>
        </li>
        <li class="nav-item">
            <?= Html::a('<i class="fa fa-cash-register"></i> ' . Yii::t('app', 'Cashbox History') .
                ' <span class="badge badge-info ml-2">' . $totalCashboxRecords . '</span>',
                ['payment-history', 'tab' => 'cashbox-history'] + $filters,
                ['class' => 'nav-link rounded-top ' . ($activeTab === 'cashbox-history' ? 'active' : '')]) ?>
        </li>
    </ul>

    <div class="tab-content border border-top-0 rounded-bottom shadow-sm">
        <div class="tab-pane fade show active p-4">
            <?php if ($dataProvider->totalCount > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="thead-light">
                        <tr>
                            <th class="text-center"><i class="fa fa-calendar"></i> <?= Yii::t('app', 'Date') ?></th>
                            <?php if ($activeTab === 'supplier-balance'): ?>
                                <th class="text-center"><i class="fa fa-user"></i> <?= Yii::t('app', 'Supplier') ?></th>
                            <?php else: ?>
                                <th class="text-center"><i class="fa fa-cash-register"></i> <?= Yii::t('app', 'Cashbox') ?></th>
                            <?php endif; ?>
                            <th class="text-center"><i class="fa fa-dollar-sign"></i> <?= Yii::t('app', 'Amount') ?></th>
                            <th class="text-center"><i class="fa fa-money-bill"></i> <?= Yii::t('app', 'Currency') ?></th>
                            <th class="text-center"><i class="fa fa-exchange-alt"></i> <?= Yii::t('app', 'Amount (USD)') ?></th>
                            <th class="text-center"><i class="fa fa-tag"></i> <?= Yii::t('app', 'Type') ?></th>
                            <th class="text-center"><i class="fa fa-user-tie"></i> <?= Yii::t('app', 'User') ?></th>
                            <th class="text-center"><i class="fa fa-comment"></i> <?= Yii::t('app', 'Description') ?></th>
                        </tr>
                        </thead>
                        <?php foreach ($dataProvider->models as $model): ?>
                            <tr>
                                <td class="text-center">
                                    <span class="badge badge-light">
                                        <?= Yii::$app->formatter->asDatetime($model->created_at, 'php:d.m.Y H:i') ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <?php if ($activeTab === 'supplier-balance'): ?>
                                        <span class="font-weight-bold text-dark">
                                            <?= Html::encode($model->suplier ? $model->suplier->full_name : 'N/A') ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="font-weight-bold text-info">
                                            <?= Html::encode($model->cashbox ? $model->cashbox->name : 'N/A') ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-right font-weight-bold">
                                    <span class="text-<?= $model->sum < 0 ? 'danger' : 'success' ?>">
                                        <?= number_format($model->sum, 2) ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-secondary">
                                        <?= Html::encode($model->currency ? $model->currency->name : 'N/A') ?>
                                    </span>
                                </td>
                                <td class="text-right font-weight-bold">
                                    <span class="text-primary">
                                        $<?= number_format(convertToDollars($model->sum, $model->currency ? $model->currency->name : ''), 2) ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <?php
                                    $typeClass = '';
                                    if ($activeTab === 'supplier-balance') {
                                        switch ($model->type) {
                                            case SuplierBalanceHistory::TYPE_INCOME:
                                                $typeClass = 'success';
                                                $typeName = Yii::t('app', 'Income');
                                                break;
                                            case SuplierBalanceHistory::TYPE_EXPENSE:
                                                $typeClass = 'danger';
                                                $typeName = Yii::t('app', 'Expense');
                                                break;
                                            case SuplierBalanceHistory::TYPE_RETURN:
                                                $typeClass = 'warning';
                                                $typeName = Yii::t('app', 'Return');
                                                break;
                                            default:
                                                $typeClass = 'secondary';
                                                $typeName = Yii::t('app', 'Unknown');
                                        }
                                    } else {
                                        switch ($model->type) {
                                            case CashboxBalanceHistory::TYPE_INCOME:
                                                $typeClass = 'success';
                                                $typeName = Yii::t('app', 'Income');
                                                break;
                                            case CashboxBalanceHistory::TYPE_EXPENSES:
                                                $typeClass = 'danger';
                                                $typeName = Yii::t('app', 'Expense');
                                                break;
                                            case CashboxBalanceHistory::TYPE_RETURN:
                                                $typeClass = 'warning';
                                                $typeName = Yii::t('app', 'Return');
                                                break;
                                            default:
                                                $typeClass = 'secondary';
                                                $typeName = Yii::t('app', 'Unknown');
                                        }
                                    }
                                    ?>
                                    <span class="badge badge-<?= $typeClass ?>">
                                        <?= $typeName ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="text-muted">
                                        <?= Html::encode($model->user ? $model->user->full_name : 'N/A') ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="text-muted">
                                        <?= Html::encode($model->description ?: '-') ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Bootstrap пагинация -->
                <div class="d-flex justify-content-center mt-4">
                    <?= LinkPager::widget([
                        'pagination' => $dataProvider->pagination,
                        'options' => ['class' => 'pagination pagination-sm'],
                        'linkOptions' => ['class' => 'page-link'],
                        'activePageCssClass' => 'active',
                        'disabledPageCssClass' => 'disabled',
                        'prevPageLabel' => '<i class="fa fa-chevron-left"></i>',
                        'nextPageLabel' => '<i class="fa fa-chevron-right"></i>',
                        'firstPageLabel' => '<i class="fa fa-angle-double-left"></i>',
                        'lastPageLabel' => '<i class="fa fa-angle-double-right"></i>',
                    ]) ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fa fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?= Yii::t('app', 'No payment history available') ?></h5>
                    <p class="text-muted"><?= Yii::t('app', 'Payment transactions will appear here') ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php Pjax::end(); ?>
</div>
<?php
// Простой JavaScript для Bootstrap пагинации
$js = <<<JS
$(document).ready(function() {
    // Обработка переключения табов
    $('.nav-tabs a').on('click', function(e) {
        e.preventDefault();
        window.location.href = $(this).attr('href');
    });

    // Анимации для карточек
    $('.card').hover(
        function() {
            $(this).addClass('shadow-lg').css('transform', 'translateY(-2px)');
        },
        function() {
            $(this).removeClass('shadow-lg').css('transform', 'translateY(0)');
        }
    );
});
JS;

$this->registerJs($js, View::POS_END);

// CSS стили для Bootstrap пагинации
$css = <<<CSS
.nav-tabs-custom .nav-link {
    border-radius: 10px 10px 0 0;
    transition: all 0.3s ease;
}

.nav-tabs-custom .nav-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.nav-tabs-custom .nav-link.active {
    background-color: #007bff;
    color: white !important;
    border-color: #007bff;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transition: background-color 0.3s ease;
}

.badge {
    font-size: 0.85em;
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-sm.rounded-pill {
    padding: 0.375rem 1rem;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Bootstrap пагинация стили */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    color: #495057;
    transition: all 0.15s ease-in-out;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}
CSS;

$this->registerCss($css);
?>