<?php

use yii\bootstrap5\Html;
use yii\web\View;
use yii\widgets\Pjax;

$this->title = Yii::t("app", "suplier_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$select = Yii::t("app", "select_currency");

$labelsJson = json_encode([
    'search' => $searchLabel,
    'lengthMenu' => $lengthMenuLabel,
    'zeroRecords' => $zeroRecordsLabel,
    'info' => $infoLabel,
    'infoEmpty' => $infoEmptyLabel,
    'infoFiltered' => $infoFilteredLabel,
    'select' => $select
]);

?>

<div class="card-body">

    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
                <a href="#" class="btn btn-primary suplier-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_supplier") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'suplier-grid-pjax']); ?>
    <?php if($dataProvider->models): ?>

        <div>
            <table id="suplier-grid-view" class="table table-bordered table-striped compact">
                <thead>
                <tr>
                    <th><?= Yii::t("app", "suplier_name") ?></th>
                    <th><?= Yii::t("app", "phone_number") ?></th>
                    <th><?= Yii::t("app", "phone_number_2") ?></th>
                    <th><?= Yii::t("app", "address") ?></th>
                    <th><?= Yii::t("app", "suplier_balance") ?></th>
                    <th><?= Yii::t("app", "created_at") ?></>
                    <th><?= Yii::t("app", "status") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($dataProvider->models as $model): ?>
                    <tr>
                        <td><?= Html::encode($model->full_name) ?></td>
                        <td><?= Html::encode($model->phone_number) ?></td>
                        <td><?= Html::encode($model->phone_number_2) ?></td>
                        <td><?= Html::encode($model->address) ?></td>
                        <td>
                            <?php
                            $totalDollarBalance = $model->getDollarBalance();
                            $formattedSum = number_format($totalDollarBalance, 2, '.', ',');
                            echo Html::tag('span', "\${$formattedSum}", ['class' => 'currency-balance text-primary']);
                            ?>
                        </td>

                        <td data-order="<?= $model->created_at ? strtotime($model->created_at) : '' ?>">
                            <?php
                            if ($model->created_at !== null) {
                                echo Html::encode(date('d.m.Y H:i', strtotime($model->created_at)));
                            } else {
                                echo 'N/A';
                            }
                            ?>
                        </td>

                        <td>
                            <span class="<?= $model->deleted_at === null ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                <?= $model->deleted_at === null ? Yii::t("app", "active") : Yii::t("app", "not_active") ?>
                            </span>
                        </td>
                        <td>

                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                    <?= Yii::t("app", "detail") ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="/backend/suplier/detail?id=<?= Html::encode($model->id) ?>" class="dropdown-item">
                                        <?= Yii::t("app", "suplier_view") ?>
                                    </a>

                                    <?php if ($model->deleted_at == NULL): ?>
                                    <a href="#" class="dropdown-item suplier-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model->id) ?>">
                                        <?= Yii::t("app", "edit") ?>
                                    </a>
                                    <?php
                                        // Отображаем кнопку "Оплата" только если есть ненулевой суммарный баланс в долларах
                                        if ($model->hasDollarBalance()): ?>
                                            <a href="#" class="dropdown-item supplier-pay" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model->id) ?>">
                                                <span class="text-success"><?= Yii::t("app", "pay") ?></span>
                                            </a>
                                        <?php endif; ?>

                                    <a href="#" class="dropdown-item suplier-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model->id) ?>">
                                        <span class="red-text"><?= Yii::t("app", "delete") ?></span>
                                    </a>


                                </div>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>

    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>

</div>

<div id="one" data-text="<?= Yii::t("app", "add_supplier") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "suplier_details") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "suplier_delete") ?>"></div>

<?php
$js = <<<JS
(function($) {
    const labels = Object.assign({}, {$labelsJson}, {
        one: $('#one').attr('data-text'),
        two: $('#two').attr('data-text'),
        three: $('#three').attr('data-text'),
        four: $('#four').attr('data-text')
    });


    let initialized = false;

    function initDataTable() {
        const table = $('#suplier-grid-view').DataTable();

        if ($.fn.DataTable.isDataTable('#suplier-grid-view')) {
            table.destroy();
        }

        $('#suplier-grid-view').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true,
            pageLength: 50,
            language: {
                paginate: {
                    previous: '<span class="custom-pagination prev"><i class="fa fa-arrow-left"></i></span>',
                    next: '<span class="custom-pagination next"><i class="fa fa-arrow-right"></i></span>'
                },
                search: labels.search,
                lengthMenu: labels.lengthMenu,
                zeroRecords: labels.zeroRecords,
                info: labels.info,
                infoEmpty: labels.infoEmpty,
                infoFiltered: labels.infoFiltered
            },
            columnDefs: [
                {
                    targets: [0, 1, 2, 6, 7],
                    orderable: false
                },
                {
                    targets: 4,
                    type: 'num',
                    render: function(data, type) {
                        if (type === 'sort' || type === 'type') {
                            return parseFloat(data.replace(/[^0-9.-]+/g, ""));
                        }
                        return data;
                    }
                }
            ],
            initComplete: function() {
                this.api().columns().every(function(index) {
                    const column = this;
                    const excludeColumns = [6, 7];

                    if (!excludeColumns.includes(index)) {
                        const select = $('<select><option value=""></option></select>')
                            .appendTo($(column.footer()))
                            .on('change', function() {
                                const val = $.fn.dataTable.util.escapeRegex($(this).val());
                                column.search(val ? '^' + val + '$' : '', true, false).draw();
                            });

                        column.data().unique().sort().each(function(d) {
                            select.append(`<option value='\${d}'>\${d}</option>`);
                        });
                    }
                });
            }
        });
    }

    function initializeDropdown() {
        $('.dropdown-toggle').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.dropdown', function(e) {
                    e.preventDefault();
                    const dropdownMenu = button.next('.dropdown-menu');
                    $('.dropdown-menu').not(dropdownMenu).hide();
                    dropdownMenu.toggle();
                });
                button.data('handler-attached', true);
            }
        });

        $(document).on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').hide();
            }
        });
    }

    function initializeSuplierCreate() {
        $('.suplier-create').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.suplier-create', function() {
                    $.ajax({
                        url: '/backend/suplier/create',
                        dataType: 'json',
                        type: 'GET',
                        success: function(response) {
                            $('#ideal-mini-modal .modal-title').html(labels.one);
                            $('#ideal-mini-modal .modal-body').html(response.content);
                            $('#ideal-mini-modal .mini-button').addClass("suplier-create-button");
                            $('#ideal-mini-modal').modal('show');
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });

        $(document).on('click.suplier-create-button', '.suplier-create-button', function() {
            const button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                const formData = $('#suplier-create-form').serialize();
                $.ajax({
                    url: '/backend/suplier/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        $('.error-container').empty();
                        if (response && response.status === 'success') {
                            $.pjax.reload({container: '#suplier-grid-pjax'});
                            $('#ideal-mini-modal').modal('hide');
                        } else if (response && response.status === 'fail') {
                            if (response.errors && response.errors.general) {
                                // Показываем ошибку через iziToast
                                iziToast.error({
                                    title: 'Хато!',
                                    message: response.errors.general,
                                    position: 'topRight'
                                });
                            } else if (response.errors) {
                                if (typeof response.errors === 'string') {
                                    $('#currency_ids-error').html(response.errors).css('color', 'red');
                                } else {
                                    $.each(response.errors, function(key, value) {
                                        $('#' + key + '-error').html(value.join(', ')).css('color', 'red');
                                    });
                                }
                                if (response.content) {
                                    $('#ideal-mini-modal .modal-body').html(response.content);
                                }
                            }
                        } else {
                            $.pjax.reload({container: '#suplier-grid-pjax'});
                            $('#ideal-mini-modal').modal('hide');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('Error occurred while processing the request:', errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeSuplierUpdate() {
        $('.suplier-update').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.suplier-update', function() {
                    const id = button.attr("data-id");
                    $.ajax({
                        url: '/backend/suplier/update',
                        dataType: 'json',
                        type: 'GET',
                        data: { id: id },
                        success: function(response) {
                            if (response) {
                                $('#ideal-mini-modal .modal-title').html(labels.two);
                                $('#ideal-mini-modal .modal-body').html(response.content);
                                $('#ideal-mini-modal .mini-button').addClass("suplier-update-button");
                                $('#ideal-mini-modal').modal('show');
                            }
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });

        $(document).on('click.suplier-update-button', '.suplier-update-button', function() {
            const button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                const formData = $('#suplier-update-form').serialize();
                $.ajax({
                    url: '/backend/suplier/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({container: '#suplier-grid-pjax'});
                            $('#ideal-mini-modal').modal('hide');
                        } else if (response.status === 'fail') {
                            if (response.errors && response.errors.general) {
                                // Показываем ошибку через iziToast
                                iziToast.error({
                                    title: 'Хато!',
                                    message: response.errors.general,
                                    position: 'topRight'
                                });
                            } else if (response.errors) {
                                $.each(response.errors, function(key, value) {
                                    $('#' + key + '-error').html(value.join(', ')).css('color', 'red');
                                });
                                if (response.content) {
                                    $('#ideal-mini-modal .modal-body').html(response.content);
                                }
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('Error occurred while processing the request:', errorThrown);
                        iziToast.error({
                            timeout: 5000,
                            icon: 'fas fa-times',
                            message: 'An error occurred while processing your request.',
                            position: 'topRight',
                            onOpening: function(instance, toast) {
                                toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                            }
                        });
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeSuplierDelete() {
        $('.suplier-delete').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.suplier-delete', function() {
                    const id = button.attr("data-id");
                    $.ajax({
                        url: '/backend/suplier/delete',
                        dataType: 'json',
                        type: 'GET',
                        data: { id: id },
                        success: function(response) {
                            if (response.status === 'fail') {
                                $('#ideal-mini-modal-delete .modal-title').html(labels.four);
                                $('#ideal-mini-modal-delete .modal-body').html(response.content);
                                $('#ideal-mini-modal-delete .mini-button').addClass("btn-danger delete-suplier-button");
                                $('#ideal-mini-modal-delete').modal('show');
                            }
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });

        $(document).on('click.delete-suplier-button', '.delete-suplier-button', function() {
            const button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                const formData = $('#suplier-delete-form').serialize();
                $.ajax({
                    url: '/backend/suplier/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status === 'success') {
                            $.pjax.reload({container: '#suplier-grid-pjax'});
                            $('#ideal-mini-modal-delete').modal('hide');
                            iziToast.success({
                                timeout: 5000,
                                icon: 'fas fa-check',
                                message: response.message,
                                position: 'topRight',
                                onOpening: function(instance, toast) {
                                    toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                }
                            });
                        } else if (response.status === 'fail') {
                            iziToast.error({
                                timeout: 5000,
                                icon: 'fas fa-exclamation-triangle',
                                message: response.message,
                                position: 'topRight',
                                onOpening: function(instance, toast) {
                                    toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                }
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeSupplierPay() {
        $('.supplier-pay').each(function() {
            const button = $(this);
            if (!button.data('handler-attached')) {
                button.on('click.supplier-pay', function() {
                    const id = button.attr("data-id");
                    $.ajax({
                        url: '/backend/suplier/pay',
                        dataType: 'json',
                        type: 'GET',
                        data: { id: id },
                        success: function(response) {
                            if (response) {
                                $('#ideal-mini-modal .modal-title').html('<?= Yii::t("app", "Payment to Supplier") ?>');
                                $('#ideal-mini-modal .modal-body').html(response.content);
                                $('#ideal-mini-modal .mini-button').addClass("supplier-payment-button");
                                $('#ideal-mini-modal').modal('show');
                            }
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            console.error('AJAX Error:', xhr.statusText, errorThrown);
                            iziToast.error({
                                title: '<?= Yii::t("app", "Error") ?>',
                                message: '<?= Yii::t("app", "Failed to load payment form") ?>',
                                position: 'topRight'
                            });
                        }
                    });
                });
                button.data('handler-attached', true);
            }
        });

        // Обработчик кнопки оплаты перенесен в файл pay.php
    }

    function initializeAll() {
        if (initialized) return;
        initialized = true;

        initDataTable();
        initializeDropdown();
        initializeSuplierCreate();
        initializeSuplierUpdate();
        initializeSuplierDelete();
        initializeSupplierPay();
    }

    $(document).ready(function() {
        initializeAll();
    });

    $(document).on('pjax:complete', function() {
    initialized = false;
    setTimeout(initializeAll, 100);
});

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>