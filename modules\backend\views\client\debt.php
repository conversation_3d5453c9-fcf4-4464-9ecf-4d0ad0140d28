<?php

use yii\grid\SerialColumn;
use yii\helpers\Html;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\helpers\Url;
use yii\widgets\Pjax;
use app\common\models\CurrencyCourse;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('app', 'debt_clients');
$this->params['breadcrumbs'][] = $this->title;

// Получаем текущий курс сума для конвертации в доллары
$sumCourse = CurrencyCourse::find()
    ->where(['currency_id' => 2]) // ID валюты "Сўм"
    ->andWhere(['<=', 'start_date', date('Y-m-d')])
    ->andWhere(['>', 'end_date', date('Y-m-d')])
    ->andWhere(['deleted_at' => null])
    ->orderBy(['start_date' => SORT_DESC])
    ->one();

$exchangeRate = $sumCourse ? $sumCourse->course : 1;

?>
<style>
    .modal-dialog {
        max-width: 600px;
    }

    /* Стиль для просроченных долгов */
    .overdue-debt {
        background-color: #ffdddd !important; /* Светло-красный фон */
        color: #cc0000 !important; /* Темно-красный текст */
        font-weight: bold; /* Жирный текст для выделения */
    }

    /* Чтобы стиль сохранялся при наведении */
    .overdue-debt:hover {
        background-color: #ffcccc !important; /* Немного более яркий красный при наведении */
    }

    /* Новый CSS для фильтров */
    .filter-group .form-control, .filter-group .btn {
        height: 36px;
        font-size: 15px;
        padding: 4px 10px;
        border-radius: 4px;
    }
    .filter-group .form-control {
        min-width: 160px;
        margin-right: 8px;
    }
    .filter-group select.form-control {
        min-width: 120px;
        max-width: 150px;
    }
    .filter-group .btn {
        margin-left: 8px;
        padding: 0 14px;
    }
    @media (max-width: 600px) {
        .filter-group { flex-direction: column; gap: 8px; }
        .filter-group .form-control, .filter-group .btn { width: 100%; margin: 0 0 8px 0; }
    }

    /* Стили для итоговой строки в таблице */
    #clients-table tfoot td {
        font-weight: bold;
        background-color: #f8f9fa;
    }

    #total-debt-uzs, #total-debt-usd {
        font-size: 1.0em;
        font-weight: bold;
        margin: 2px 0;
    }

    #total-debt-uzs {
        color: #007bff;
    }

    #total-debt-usd {
        color: #28a745;
    }
</style>

<div class="card-body">

    <?php Pjax::begin(['id' => 'client-pjax']); ?>        <div class="row align-items-center mb-3">
            <div class="col-md-12">
                <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
            </div>
        </div>
          <!-- Фильтры -->
        <div class="row mb-3 justify-content-end">
            <div class="col-md-7 d-flex justify-content-end align-items-center">
                <div class="filter-group d-flex align-items-center" style="max-width: 500px; width: 100%;">
                    <input type="text" id="client-search" class="form-control" placeholder="<?= Yii::t('app', 'search_by_name_or_phone') ?>">
                    <select id="seller-filter" class="form-control">
                        <option value=""><?= Yii::t('app', 'all_sellers') ?></option>
                        <?php
                        // Получаем уникальных продавцов из данных
                        $sellers = [];
                        if (!empty($model)) {
                            foreach ($model as $item) {
                                if (!empty($item['add_user_name']) && !in_array($item['add_user_name'], $sellers)) {
                                    $sellers[] = $item['add_user_name'];
                                }
                            }
                            sort($sellers);
                            foreach ($sellers as $seller): ?>
                                <option value="<?= Html::encode($seller) ?>"><?= Html::encode($seller) ?></option>
                            <?php endforeach;
                        }
                        ?>
                    </select>
                    <button class="btn btn-outline-secondary" type="button" id="search-button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>

            <?php if (!empty($model)): ?>
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="clients-table">
                    <thead>
                    <tr>
                        <th><?= Yii::t("app", "client_name"); ?></th>
                        <th><?= Yii::t("app", "phone_number"); ?></th>
                        <th><?= Yii::t("app", "phone_number_2"); ?></th>
                        <th><?= Yii::t("app", "address"); ?></th>
                        <th><?= Yii::t("app", "debt"); ?></th>
                        <th><?= Yii::t("app", "seller"); ?></th>
                        <th><?= Yii::t("app", "Date of debt repayment"); ?></th>
                        <th style="width: 300px;"><?= Yii::t("app", "actions"); ?></th>
                    </tr>
                    </thead>
                    <tbody id="clients-tbody">
                    <?php foreach ($model as $key => $value):
                        // Проверяем, просрочена ли дата возврата долга или сегодня
                        $isOverdue = false;
                        if (!empty($value['debt_return_date'])) {
                            $returnDate = strtotime($value['debt_return_date']);
                            $today = strtotime(date('Y-m-d'));
                            $isOverdue = ($returnDate <= $today);
                        }
                        $rowClass = $isOverdue ? 'overdue-debt' : '';
                    ?>
                        <tr class="<?= $rowClass ?>">
                            <td><?= $value['full_name']; ?></td>
                            <td><?= $value['phone_number']; ?></td>
                            <td><?= $value['phone_number_2']; ?></td>
                            <td><?= $value['address']; ?></td>
                            <td><?= $value['total_debt']; ?></td>
                            <td><?= $value['add_user_name'] ? $value['add_user_name'] : ''; ?></td>
                            <td>
                                <?= $value['debt_return_date'] ? date('d.m.Y', strtotime($value['debt_return_date'])) : ''; ?>
                            </td>
                            <td>
                                <div class="dropdown d-inline">
                                    <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                        <?= Yii::t("app", "detail") ?>
                                    </a>

                                    <div class="dropdown-menu">
                                        <a href="<?= Url::to(['/backend/client/view', 'id' => $value['id']]) ?>" class="dropdown-item">
                                            <?= Yii::t("app", "client_view") ?>
                                        </a>
                                        <a href="#" class="dropdown-item client-pay" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($value['id']) ?>" data-order-id="<?= Html::encode($value['order_id']) ?>">
                                            <?= Yii::t('app', 'pay') ?>
                                        </a>
                                        <a href="#" class="dropdown-item change-date" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($value['id']) ?>">
                                            <?= Yii::t('app', 'change_date') ?>
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach ?>
                    </tbody>
                    <!-- Добавление footer для отображения общей суммы долга -->
                    <tfoot>
                        <tr class="table-info font-weight-bold">
                            <td colspan="4" class="text-right"><?= Yii::t("app", "total_debt"); ?>:</td>
                            <td class="text-right">
                                <div id="total-debt-uzs" class="text-primary">0 UZS</div>
                                <div id="total-debt-usd" class="text-success">0 USD</div>
                            </td>
                            <td colspan="3"></td>
                        </tr>
                    </tfoot>
                </table>

                <!-- Пагинация Bootstrap -->
                <div class="pagination-container mt-3">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Пагинация будет добавлена через JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        <?php else: ?>
            <p><?= Yii::t("app", "no_data_available") ?></p>
        <?php endif; ?>
    <?php Pjax::end(); ?>

</div>

<div id="one" data-text="<?= Yii::t("app", "client_pay") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "change_date") ?>"></div>

<?php
$js = <<<JS

var pay = $('#one').attr('data-text');
var change_date = $('#two').attr('data-text');
var exchangeRate = {$exchangeRate}; // Курс сума к доллару

$(document).ready(function() {
    $('.filters').select2();

    initPagination();
    initializeAllHandlers();    $('#seller-filter').select2({
        width: 'resolve',
        minimumResultsForSearch: 10,
        placeholder: $('#seller-filter option:first').text(),
        allowClear: true
    });

    $(document).on('pjax:success', '#client-pjax', function() {
        initPagination();
        initializeAllHandlers();

        $('#seller-filter').select2({
            width: 'resolve',
            minimumResultsForSearch: 10,
            placeholder: $('#seller-filter option:first').text(),
            allowClear: true
        });
    });
});

function initializeAllHandlers() {
    handleClientPayClick();
    handleClientPayButtonClick();
    handleCreateClientClick();
    handleSubmitCreateClientClick();
    initializeChangeDate();
    initializeDropdown();      // Фильтр по продавцам при нажатии на кнопку
    $('#seller-filter-button').on('click', function() {
        applyFilters();
    });
}

function initializeChangeDate() {
    $('.change-date').each(function() {
        var button = $(this);
        if (!button.data('handler-attached')) {
            button.on('click', function() {
                var id = $(this).attr("data-id");
                $.ajax({
                    url: '/backend/client/change-date',
                    dataType: 'json',
                    type: 'GET',
                    data: { id: id },
                    success: function(response) {
                        if (response) {
                            $('#ideal-mini-modal .modal-title').html(change_date);
                            $('#ideal-mini-modal .modal-body').html(response.content);
                            $('#ideal-mini-modal .mini-button').addClass("btn-danger change-date-button");
                            $('#ideal-mini-modal').modal('show');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            });
            button.data('handler-attached', true);
        }
    });

    $(document).off('click.change-date-button').on('click.change-date-button', '.change-date-button', function() {
        var button = $(this);
        if (!button.prop('disabled')) {
            button.prop('disabled', true);
            var formData = $('#change-date-form').serialize();
            $.ajax({
                url: '/backend/client/change-date',
                dataType: 'json',
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.status === 'success') {
                        $.pjax.reload({ container: '#client-pjax' });
                        button.prop('disabled', false);
                        $('#ideal-mini-modal').modal('hide');
                    } else if (response.status === 'error') {
                        button.prop('disabled', false);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        iziToast.error({
                            timeout: 5000,
                            icon: 'fas fa-exclamation-triangle',
                            message: response.message,
                            position: 'topRight',
                            onOpening: function(instance, toast) {
                                toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                            }
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    button.prop('disabled', false);
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        }
    });
}

function initializeDropdown() {
    $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
        e.preventDefault();
        var dropdownMenu = $(this).next('.dropdown-menu');
        $('.dropdown-menu').not(dropdownMenu).hide();
        dropdownMenu.toggle();
    });

    $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
        if (!$(e.target).closest('.dropdown').length) {
            $('.dropdown-menu').hide();
        }
    });
}

function handleClientPayClick() {
    $('.client-pay').each(function() {
        var button = $(this);
        if (!button.data('handler-attached')) {
            button.on('click', function() {
                $.ajax({
                    url: '/backend/client-payment/pay',
                    dataType: 'json',
                    type: 'GET',
                    data: {
                        id: $(this).data('id'),
                        order_id: $(this).data('order-id')
                    },
                    success: function(response) {
                        if (response.status === 'fail') {
                            $('#ideal-mini-modal .modal-title').html(pay);
                            $('#ideal-mini-modal .modal-body').html(response.content);
                            $('#ideal-mini-modal .mini-button').addClass("client-payment-button");
                            $('#ideal-mini-modal').modal('show');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.error("AJAX Error:", textStatus, errorThrown);
                        alert("An error occurred while processing the request. Please try again.");
                    }
                });
            });
            button.data('handler-attached', true);
        }
    });
}

function handleClientPayButtonClick() {
    $(document).off('click.client-payment-button').on('click.client-payment-button', '.client-payment-button', function() {
        var button = $(this);
        if (!button.prop('disabled')) {
            button.prop('disabled', true);
            var formData = $('#user-pay-form').serialize();
            $.ajax({
                url: '/backend/client-payment/pay',
                dataType: 'json',
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false);

                    if (response && response.status === 'success') {
                        button.prop('disabled', false);
                        $.pjax.reload({container: '#client-pjax'});
                        $('.close').trigger('click');
                        $('#ideal-mini-modal').modal('hide');

                        handleSuccessfulPayment(response);
                    } else if (response && response.status === 'fail') {
                        button.prop('disabled', false);
                        $('.error-message').empty();

                        $.each(response.message, function(key, messages) {
                            var fieldName = key.replace(/currencies\[\d+\]\[([^\]]+)\]/, '$1');

                            var errorDiv = $('#' + fieldName + '-error');
                            if (errorDiv.length) {
                                errorDiv.html('<span class="error-text">' + messages.join('<br>') + '</span>');
                            }
                        });
                    } else {
                        $.pjax.reload({container: '#client-pjax'});
                        $('.close').trigger('click');
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error("AJAX Error:", textStatus, errorThrown);
                    alert("An error occurred. Please try again.");
                    button.prop('disabled', false);
                }
            });
        }
    });
}


function handleSuccessfulPayment(response) {
    if (response.savedPayments?.length > 0) {
        if (confirm('Чек чиқаришни хохлайсизми?')) {
            printOrder(response.savedPayments);
        }
    }
}

function printOrder(savedPayments) {
    const printWindow = window.open('/backend/chek-printer/print', '_blank', 'width=800,height=600');
    printWindow.onload = function() {
        fetch('/backend/chek-printer/print', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ savedPayments: savedPayments })
        })
        .then(response => response.text())
        .then(data => {
            printWindow.document.write(data);
            printWindow.document.close();
            printWindow.print();
        })
        .catch(error => console.error('Ошибка при отправке данных:', error));
    };
}



function handleCreateClientClick() {
    $(document).on('click', '#create-client', function () {
        $('#exampleModal3 .modal-body').html('');
        $('#exampleModal3 .btn-primary').removeClass().addClass("btn btn-primary create-client");
        $.ajax({
            url: '/backend/client/create',
            dataType: 'json',
            type: 'get',
            success: function (response) {
                if (response.status === 'error') {
                    $('#exampleModal3 .modal-title').html(response.add_client).css({fontSize: 23});
                    $('#exampleModal3 .modal-body').html(response.content);
                    $(function() {
                        $('#client-districts_id').select2({
                            placeholder: 'Viloyat yoki tuman tanlang',
                            dropdownParent: $('#exampleModal3 .modal-body')
                        });
                    });
                }
            }
        });
    });
}

function handleSubmitCreateClientClick() {
    $(document).on('click', '.create-client', function (event) {
        event.preventDefault();
        const form = $('#client-form');
        $.ajax({
            url: '/backend/client/create',
            dataType: 'json',
            type: 'post',
            data: form.serialize(),
            success: function (response) {
                if (response.status == 'error') {
                    $('#exampleModal3 .modal-body').html(response.content);
                } else if (response.status == 'success') {
                    $("#exampleModal3").modal('hide');
                    $.pjax.reload({container: "#client-pjax"});
                } else if (response.status == 'error_phone') {
                    $('.field-client-phone_number .help-block').text('Bu raqam avvalroq band qilingan!');
                }
            }
        });
    });
}

// Переменные для пагинации
var currentPage = 1;
var itemsPerPage = 20;
var filteredClients = [];
var totalDebtUZS = 0; // Переменная для хранения общей суммы долга в сумах
var totalDebtUSD = 0; // Переменная для хранения общей суммы долга в долларах

// Инициализация пагинации и поиска
function initPagination() {
    // Копируем все клиенты для фильтрации
    filteredClients = [];
    $('#clients-tbody tr').each(function() {
        filteredClients.push($(this));
    });

    // Вычисляем общую сумму долга при первой загрузке
    calculateTotalDebt();

    // Показываем первую страницу
    displayPage(currentPage);

    // Инициализируем поиск
    $('#search-button').on('click', function() {
        applyFilters();
    });

    // Поиск при нажатии Enter
    $('#client-search, #seller-filter').on('keypress', function(e) {
        if (e.which === 13) {
            applyFilters();
        }
    });
}

// Функция для расчета общей суммы долга
function calculateTotalDebt() {
    totalDebtUZS = 0;
    totalDebtUSD = 0;

    // Используем только отфильтрованных клиентов для расчета общей суммы
    filteredClients.forEach(function(row) {
        var debtText = row.find('td:eq(4)').text().trim();
        var debtValue = parseFloat(debtText.replace(/\s+/g, '').replace(/,/g, '.')) || 0;
        totalDebtUZS += debtValue;
    });

    // Конвертируем в доллары
    if (exchangeRate > 0) {
        totalDebtUSD = totalDebtUZS / exchangeRate;
    }

    // Форматируем суммы
    var formattedTotalDebtUZS = totalDebtUZS.toLocaleString('ru-RU', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });

    var formattedTotalDebtUSD = totalDebtUSD.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });

    // Обновляем значения в footer
    $('#total-debt-uzs').text(formattedTotalDebtUZS + ' UZS');
    $('#total-debt-usd').text(formattedTotalDebtUSD + ' USD');
}

// Универсальная функция применения всех фильтров
function applyFilters() {
    var searchText = $('#client-search').val().toLowerCase();
    var selectedSeller = $('#seller-filter').val();

    // Фильтруем клиентов по всем критериям
    filteredClients = [];
    $('#clients-tbody tr').each(function() {
        var row = $(this);
        var clientName = row.find('td:eq(0)').text().toLowerCase();
        var phoneNumber = row.find('td:eq(1)').text().toLowerCase();
        var phoneNumber2 = row.find('td:eq(2)').text().toLowerCase();
        var sellerName = row.find('td:eq(5)').text();

        // Поиск по имени и телефону в одном поле
        var namePhoneMatch = !searchText ||
            clientName.includes(searchText) ||
            phoneNumber.includes(searchText) ||
            phoneNumber2.includes(searchText);

        var sellerMatch = !selectedSeller || sellerName === selectedSeller;

        if (namePhoneMatch && sellerMatch) {
            filteredClients.push(row);
        }
    });

    // Пересчитываем общую сумму долга после применения фильтра
    calculateTotalDebt();

    // Сбрасываем на первую страницу и отображаем результаты
    currentPage = 1;
    displayPage(currentPage);
}

// Функция поиска клиентов (устаревшая, заменена на applyFilters)
function searchClients() {
    applyFilters();
}

// Отображение страницы
function displayPage(page) {
    // Скрываем все строки
    $('#clients-tbody tr').hide();

    // Вычисляем индексы для текущей страницы
    var startIndex = (page - 1) * itemsPerPage;
    var endIndex = Math.min(startIndex + itemsPerPage, filteredClients.length);

    // Показываем только строки для текущей страницы
    for (var i = startIndex; i < endIndex; i++) {
        filteredClients[i].show();
    }

    // Обновляем пагинацию
    updatePagination();
}

// Обновление пагинации
function updatePagination() {
    var totalPages = Math.ceil(filteredClients.length / itemsPerPage);
    var paginationHtml = '';

    // Кнопка "Предыдущая"
    paginationHtml += '<li class="page-item ' + (currentPage === 1 ? 'disabled' : '') + '">';
    paginationHtml += '<a class="page-link" href="#" data-page="prev" aria-label="Previous">';
    paginationHtml += '<span aria-hidden="true">&laquo;</span></a></li>';

    // Номера страниц
    for (var i = 1; i <= totalPages; i++) {
        paginationHtml += '<li class="page-item ' + (i === currentPage ? 'active' : '') + '">';
        paginationHtml += '<a class="page-link" href="#" data-page="' + i + '">' + i + '</a></li>';
    }

    // Кнопка "Следующая"
    paginationHtml += '<li class="page-item ' + (currentPage === totalPages || totalPages === 0 ? 'disabled' : '') + '">';
    paginationHtml += '<a class="page-link" href="#" data-page="next" aria-label="Next">';
    paginationHtml += '<span aria-hidden="true">&raquo;</span></a></li>';

    // Обновляем HTML пагинации
    $('#pagination').html(paginationHtml);

    // Добавляем обработчики событий для кнопок пагинации
    $('.page-link').on('click', function(e) {
        e.preventDefault();
        var page = $(this).data('page');

        if (page === 'prev') {
            if (currentPage > 1) {
                currentPage--;
            }
        } else if (page === 'next') {
            if (currentPage < totalPages) {
                currentPage++;
            }
        } else {
            currentPage = page;
        }

        displayPage(currentPage);
    });
}

JS;
$this->registerJs($js);
?>
