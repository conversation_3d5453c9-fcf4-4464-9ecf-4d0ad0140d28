<?php

namespace app\common\models;

use app\common\traits\SoftDelete;
use Yii;

/**
 * This is the model class for table "suplier".
 *
 * @property int $id
 * @property string|null $full_name
 * @property string|null $phone_number
 * @property string|null $phone_number_2
 * @property string|null $address
 * @property string|null $created_at
 * @property string|null $deleted_at
 */
class Suplier extends \yii\db\ActiveRecord
{

    use SoftDelete;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'suplier';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['full_name', 'phone_number', 'address'], 'required'],
            [['created_at', 'deleted_at'], 'safe'],
            [['full_name'], 'string', 'max' => 255],
            [[ 'address'], 'string', 'max' => 35],
            [['phone_number', 'phone_number_2'], 'validatePhoneNumber'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'full_name' => Yii::t('app', 'suplier_name'),
            'phone_number' => Yii::t('app', 'phone_number'),
            'phone_number_2' => Yii::t('app', 'phone_number_2'),
            'address' => Yii::t('app', 'address'),
            'created_at' => 'Created At',
            'deleted_at' => 'Deleted At',
        ];
    }

    public function validatePhoneNumber($attribute, $params, $validator)
    {
        $value = $this->$attribute;
        $numericValue = preg_replace('/\D/', '', $value);

        if (strlen($numericValue) !== 9) {
            $this->addError($attribute, Yii::t('app', 'Номер телефона должен содержать 12 цифр.'));
            return;
        }

        $companyCodes = ['90', '91', '93', '94', '88', '55', '87', '95', '97', '99', '22'];

        $operatorCode = substr($numericValue, 0, 2);

        if (!in_array($operatorCode, $companyCodes)) {
            $this->addError($attribute, Yii::t('app', "Рақам коди нотўғри"));
        }
    }


    public function getBalancesWithCurrency()
    {
        return $this->hasMany(SuplierBalance::class, ['suplier_id' => 'id'])
            ->joinWith('currency')
            ->andWhere(['suplier_balance.deleted_at' => null]);
    }



    public function getBalances()
    {
        return $this->hasMany(SuplierBalance::class, ['suplier_id' => 'id']);
    }

    public function getBalanceHistories()
    {
        return $this->hasMany(SuplierBalanceHistory::class, ['suplier_id' => 'id']);
    }

    /**
     * Получить текущий курс валюты
     * @param int $currencyId ID валюты
     * @return float
     */
    private function getCurrencyCourse($currencyId)
    {
        $course = CurrencyCourse::find()
            ->where(['currency_id' => $currencyId])
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere(['>', 'end_date', date('Y-m-d')])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        return $course ? (float)$course->course : 1;
    }

    /**
     * Конвертировать сумму в доллары
     * @param float $amount Сумма
     * @param int $fromCurrencyId ID валюты, из которой конвертируем
     * @return float
     */
    private function convertToDollar($amount, $fromCurrencyId)
    {
        // Получаем валюту доллара
        $dollarCurrency = Currency::findOne(['name' => 'Dollar']);
        if (!$dollarCurrency) {
            return 0;
        }

        // Если уже в долларах, возвращаем как есть
        if ($fromCurrencyId == $dollarCurrency->id) {
            return (float)$amount;
        }

        // Получаем курс валюты
        $course = $this->getCurrencyCourse($fromCurrencyId);

        // Если курс 0 или не найден, возвращаем 0
        if ($course <= 0) {
            return 0;
        }

        // Конвертируем в доллары (делим на курс)
        return (float)($amount / $course);
    }

    /**
     * Получить суммарный баланс поставщика в долларах (конвертированный из всех валют)
     * @return float
     */
    public function getDollarBalance()
    {
        $balances = $this->getBalancesWithCurrency()->all();
        $totalDollarBalance = 0;

        foreach ($balances as $balance) {
            if ($balance->sum != 0) {
                $dollarAmount = $this->convertToDollar($balance->sum, $balance->currency_id);
                $totalDollarBalance += $dollarAmount;
            }
        }

        return round($totalDollarBalance, 2);
    }

    /**
     * Проверить, есть ли у поставщика ненулевой суммарный баланс в долларах
     * @return bool
     */
    public function hasDollarBalance()
    {
        return abs($this->getDollarBalance()) > 0.01; // Учитываем погрешности округления
    }
}
