<?php

namespace app\modules\backend\controllers;

use app\common\models\ActionLog;
use app\common\models\CashboxBalance;
use app\common\models\CashboxBalanceHistory;
use app\common\models\CashboxBalanceInouts;
use app\common\models\Currency;
use app\common\models\CurrencyCourse;
use app\common\models\StorageBalanceHistory;
use app\common\models\Suplier;
use app\common\models\SuplierBalance;
use app\common\models\SuplierBalanceHistory;
use app\modules\backend\models\SupplierPayForm;
use Exception;
use Yii;
use yii\data\ArrayDataProvider;
use yii\web\Response;

class SuplierController extends BaseController
{

    public function actionIndex()
    {
        $supliers = Suplier::findWithDeleted()
            ->orderBy(['created_at' => SORT_DESC])->all();

        $dataProvider = new ArrayDataProvider([
            'allModels' => $supliers,
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionCreate()
    {
        $model = new Suplier();
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model->load($data);

            $transaction = Yii::$app->db->beginTransaction();
            try {
                if (!$model->validate()) {
                    return $this->asJson([
                        'status' => 'fail',
                        'errors' => $model->getErrors(),
                    ]);
                }

                if (!empty($model->phone_number) && !str_starts_with($model->phone_number, '+998')) {
                    $model->phone_number = '+998' . preg_replace('/\D/', '', $model->phone_number);
                }
                if (!empty($model->phone_number_2) && !str_starts_with($model->phone_number_2, '+998')) {
                    $model->phone_number_2 = '+998' . preg_replace('/\D/', '', $model->phone_number_2);
                }


                $existingSuplier = Suplier::find()
                ->where(['or',
                    ['phone_number' => $model->phone_number],
                    ['phone_number_2' => $model->phone_number],
                ])
                ->andWhere(['is not', 'phone_number', null])
                ->andWhere(['is not', 'phone_number_2', null])
                ->one();

                if ($existingSuplier) {
                    return [
                        'status' => 'fail',
                        'errors' => [
                            'general' => Yii::t('app', 'suplier_already_exists')
                        ]
                    ];
                }

                if (!$model->save(false)) {
                    throw new \Exception(Yii::t('app', 'Failed to save suplier.'));
                }
                $currencyIds = array_column(Currency::find()
                    ->select(['id'])
                    ->where(['deleted_at' => null])
                    ->all(), 'id');


                foreach ($currencyIds as $currencyId) {
                    $balance = new SuplierBalance();
                    $balance->suplier_id = $model->id;
                    $balance->currency_id = $currencyId;
                    $balance->sum = 0;
                    $balance->created_at = date('Y-m-d H:i:s');

                    if (!$balance->save()) {
                        throw new \Exception(Yii::t('app', 'Some balances could not be saved.'));
                    }
                }

                if (!ActionLog::logAction(Yii::$app->user->id, ActionLog::TYPE_CREATE, $model->attributes)) {
                    throw new \Exception(Yii::t('app', 'Failed to log the action.'));
                }

                $transaction->commit();
                return $this->asJson([
                    'status' => 'success',
                    'message' => Yii::t('app', 'Suplier added successfully.'),
                ]);
            } catch (\Exception $e) {
                $transaction->rollBack();
                return $this->asJson([
                    'status' => 'fail',
                    'errors' => $e->getMessage(),
                ]);
            }
        }

        if (Yii::$app->request->isGet) {

            return $this->asJson([
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => $model,
                ]),
            ]);
        }
    }


    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = Suplier::findOne($data['Suplier']['id']);

            if (!$model) {
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Suplier not found.'),
                ];
            }

            $oldData = $model->attributes;
            $model->load($data);

            $transaction = Yii::$app->db->beginTransaction();
            try {
                if (!$model->validate()) {
                    return [
                        'status' => 'fail',
                        'errors' => $model->getErrors(),
                    ];
                }

                if (!empty($model->phone_number) && !str_starts_with($model->phone_number, '+998')) {
                    $model->phone_number = '+998' . preg_replace('/\D/', '', $model->phone_number);
                }
                if (!empty($model->phone_number_2) && !str_starts_with($model->phone_number_2, '+998')) {
                    $model->phone_number_2 = '+998' . preg_replace('/\D/', '', $model->phone_number_2);
                }

                $existingSuplier = Suplier::find()
                    ->where(['or',
                        ['phone_number' => $model->phone_number],
                        ['phone_number_2' => $model->phone_number],
                    ])
                    ->andWhere(['is not', 'phone_number', null])
                    ->andWhere(['is not', 'phone_number_2', null])
                    ->andWhere(['!=', 'id', $model->id])
                    ->one();

                if ($existingSuplier) {
                    return [
                        'status' => 'fail',
                        'errors' => [
                            'general' => Yii::t('app', 'suplier_already_exists')
                        ]
                    ];
                }

                if ($model->save(false)) {
                    ActionLog::logAction(Yii::$app->user->id, ActionLog::TYPE_UPDATE, $oldData, $model->attributes);

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'Suplier updated successfully.'),
                    ];
                }

                $transaction->rollBack();
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Failed to save the suplier.'),
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'fail',
                    'message' => $e->getMessage(),
                ];
            }
        }

        if (Yii::$app->request->isGet) {
            $data = Yii::$app->request->get();
            if (!isset($data['id'])) {
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'ID is required.'),
                ];
            }

            $model = Suplier::findOne($data['id']);
            if (!$model) {
                return [
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Suplier not found.'),
                ];
            }

            $selectedCurrencyIds = SuplierBalance::find()
                ->select('currency_id')
                ->where(['suplier_id' => $model->id, 'deleted_at' => null])
                ->column();

            $model->phone_number = substr($model->phone_number, 4);
            $model->phone_number_2 = substr($model->phone_number_2, 4);

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'selectedCurrencyIds' => $selectedCurrencyIds,
                ]),
            ];
        }

        return [
            'status' => 'fail',
            'message' => Yii::t('app', 'Invalid request method.'),
        ];
    }

    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['Suplier']['id'] ?? null;

            if (!$id) {
                return $this->asJson([
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Invalid supplier ID.'),
                ]);
            }

            $model = Suplier::findOne($id);

            if (!$model) {
                return $this->asJson([
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Supplier not found.'),
                ]);
            }

            $hasRecords = StorageBalanceHistory::find()
                ->where(['suplier_id' => $model->id])
                ->exists();

            if ($hasRecords) {
                return $this->asJson([
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Cannot delete supplier. Records exist in storage balance history.'),
                ]);
            }

            $oldData = $model->attributes;

            if ($model->softDelete()) {

                ActionLog::logAction(Yii::$app->user->id, ActionLog::TYPE_DELETE, $oldData);

                return $this->asJson([
                    'status' => 'success',
                    'message' => Yii::t('app', 'Supplier deleted successfully.'),
                ]);
            } else {
                return $this->asJson([
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Failed to delete supplier.'),
                ]);
            }
        }

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Suplier::findOne($id);

            if (!$model) {
                return $this->asJson([
                    'status' => 'fail',
                    'message' => Yii::t('app', 'Supplier not found.'),
                ]);
            }

            return $this->asJson([
                'status' => 'fail',
                'content' => $this->renderPartial('delete', ['model' => $model]),
            ]);
        }

        return $this->asJson([
            'status' => 'fail',
            'message' => Yii::t('app', 'Invalid request method.'),
        ]);
    }


    /**
     * Конвертирует сумму из долларов в сумы
     */
    public function actionConvertCurrency()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $amount = Yii::$app->request->post('amount');

            try {
                $sumCourse = CurrencyCourse::find()
                ->where(['currency_id' => 2])
                ->andWhere(['<=', 'start_date', date('Y-m-d')])
                ->andWhere(['>', 'end_date', date('Y-m-d')])
                ->andWhere(['deleted_at' => null])
                ->orderBy(['start_date' => SORT_DESC])
                ->one();

                if (!$sumCourse) {
                    throw new Exception('Currency course not found');
                }

                $convertedAmount = $amount * $sumCourse->course;

                return [
                    'status' => 'success',
                    'convertedAmount' => $convertedAmount
                ];
            } catch (Exception $e) {
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return [
            'status' => 'error',
            'message' => 'Invalid request method'
        ];
    }

    /**
     * Оплата поставщику
     */
    public function actionPay()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $form = new SupplierPayForm();
            $data = Yii::$app->request->post();

            $form->load($data, '');

            if (!isset($form['currencies']) || empty($form['currencies'])) {
                return [
                    'status' => 'fail',
                    'message' => 'Отсутствуют данные о валютах',
                ];
            }

            $supplier_id = $form['currencies'][0]['suplier_id'] ?? null;

            if (!$supplier_id) {
                return [
                    'status' => 'fail',
                    'message' => 'Не указан ID поставщика',
                ];
            }

            if (!$form->validate()) {
                return [
                    'status' => 'fail',
                    'message' => $form->getErrors(),
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();

            try {
                // Получаем информацию о долге поставщика до оплаты
                $dollarBalanceBefore = SuplierBalance::findOne([
                    'suplier_id' => $supplier_id,
                    'currency_id' => 2, // Доллары
                ]);
                $dollarDebtBefore = $dollarBalanceBefore ? $dollarBalanceBefore->sum : 0;

                // Получаем все балансы поставщика до оплаты
                $allBalancesBefore = SuplierBalance::find()
                    ->where(['suplier_id' => $supplier_id])
                    ->all();

                $balanceInfoBefore = "Балансы поставщика ДО оплаты:\n";
                foreach ($allBalancesBefore as $balance) {
                    $currency = Currency::findOne($balance->currency_id);
                    $currencyName = $currency ? $currency->name : "Валюта ID: {$balance->currency_id}";
                    $balanceInfoBefore .= "- {$currencyName}: {$balance->sum}\n";
                }


                // Обработка платежа
                $this->processCashboxBalance($form);
                $this->processSupplierBalance($form);

                // Получаем информацию о долге поставщика после оплаты
                $dollarBalanceAfter = SuplierBalance::findOne([
                    'suplier_id' => $supplier_id,
                    'currency_id' => 2, // Доллары
                ]);
                $dollarDebtAfter = $dollarBalanceAfter ? $dollarBalanceAfter->sum : 0;

                // Получаем все балансы поставщика после оплаты
                $allBalancesAfter = SuplierBalance::find()
                    ->where(['suplier_id' => $supplier_id])
                    ->all();

                $balanceInfoAfter = "Балансы поставщика ПОСЛЕ оплаты:\n";
                foreach ($allBalancesAfter as $balance) {
                    $currency = Currency::findOne($balance->currency_id);
                    $currencyName = $currency ? $currency->name : "Валюта ID: {$balance->currency_id}";
                    $balanceInfoAfter .= "- {$currencyName}: {$balance->sum}\n";
                }



                // Логируем действие оплаты
                $logData = [
                    'supplier_id' => $supplier_id,
                    'payment_date' => date('Y-m-d H:i:s'),
                    'currencies' => $form['currencies'],
                    'dollar_debt_before' => $dollarDebtBefore,
                    'dollar_debt_after' => $dollarDebtAfter
                ];

                ActionLog::logAction(
                    Yii::$app->user->id,
                    ActionLog::TYPE_PAYMENT,
                    null,
                    json_encode($logData)
                );

                $transaction->commit();


                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'payment_successful'),
                ];
            } catch (Exception $e) {
                $transaction->rollBack();


                return [
                    'status' => 'fail',
                    'message' => $e->getMessage(),
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $supplier_id = Yii::$app->request->get('id');
            $supplier = Suplier::findOne($supplier_id);

            if (!$supplier) {
                return [
                    'status' => 'fail',
                    'message' => 'Поставщик не найден',
                ];
            }

            // Получаем информацию о балансе поставщика для отображения
            $supplierBalanceInfo = SuplierBalance::find()
                ->where(['suplier_id' => $supplier_id, 'currency_id' => 2])
                ->one();

            $supplierBalanceInSum = $supplierBalanceInfo ? $supplierBalanceInfo->sum : 0;

            // Получаем текущий курс валюты
            $course = CurrencyCourse::find()
                ->where(['currency_id' => 2])
                ->andWhere(['<=', 'start_date', date('Y-m-d')])
                ->andWhere(['>', 'end_date', date('Y-m-d')])
                ->andWhere(['deleted_at' => null])
                ->orderBy(['start_date' => SORT_DESC])
                ->one();

            // Получаем список всех валют для формы оплаты
            $currencies = Currency::find()->where(['deleted_at' => null])->all();

            // Получаем список всех касс
            $cashbox = \app\common\models\Cashbox::find()->where(['deleted_at' => null])->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('pay', [
                    'supplier' => $supplier,
                    'supplierBalanceInSum' => $supplierBalanceInSum,
                    'course' => $course,
                    'currencies' => $currencies,
                    'cashbox' => $cashbox
                ])
            ];
        }
    }

    /**
     * Обрабатывает баланс кассы при оплате поставщику
     */
    private function processCashboxBalance($payments)
    {
        foreach ($payments['currencies'] as $payment) {
            if (!isset($payment['currency_id']) || !isset($payment['sum']) || empty($payment['sum'])) {
                continue;
            }

            $payment['sum'] = (float)str_replace([' ', ','], ['', '.'], $payment['sum']);

            if (!is_numeric($payment['sum']) || $payment['sum'] <= 0) {
                continue;
            }

            $cashboxBalance = CashboxBalance::findOne([
                'cashbox_id' => $payment['cashbox_id'],
                'currency_id' => $payment['currency_id'],
            ]);

            if (!$cashboxBalance) {
                $cashboxBalance = new CashboxBalance([
                    'cashbox_id' => $payment['cashbox_id'],
                    'currency_id' => $payment['currency_id'],
                    'sum' => 0,
                ]);

                if (!$cashboxBalance->save()) {
                    throw new Exception('Не удалось создать баланс кассы: ' . json_encode($cashboxBalance->getErrors()));
                }
            }

            $cashboxBalance->sum -= $payment['sum'];

            if (!$cashboxBalance->save()) {
                throw new Exception('Не удалось обновить баланс кассы: ' . json_encode($cashboxBalance->getErrors()));
            }

            $cashboxBalanceHistory = new CashboxBalanceHistory();
            $cashboxBalanceHistory->cashbox_balance_id = $cashboxBalance->id;
            $cashboxBalanceHistory->cashbox_id = $payment['cashbox_id'];
            $cashboxBalanceHistory->currency_id = $payment['currency_id'];
            $cashboxBalanceHistory->user_id = Yii::$app->user->id;
            $cashboxBalanceHistory->sum = $payment['sum'];
            $cashboxBalanceHistory->type = CashboxBalanceHistory::TYPE_EXPENSES;
            $cashboxBalanceHistory->description = "Оплата поставщику";
            $cashboxBalanceHistory->module_type = CashboxBalanceHistory::MODULE_SUPPLIER_TYPE;
            $cashboxBalanceHistory->created_at = date('Y-m-d H:i');

            if (!$cashboxBalanceHistory->save()) {
                throw new Exception('Не удалось сохранить историю баланса кассы: ' . json_encode($cashboxBalanceHistory->getErrors()));
            }

            $cashboxBalanceInouts = new CashboxBalanceInouts();
            $cashboxBalanceInouts->cashbox_id = $payment['cashbox_id'];
            $cashboxBalanceInouts->currency_id = $payment['currency_id'];
            $cashboxBalanceInouts->sum = -$payment['sum'];
            $cashboxBalanceInouts->date = date('Y-m-d');

            if (!$cashboxBalanceInouts->save()) {
                throw new Exception('Не удалось сохранить исходящий платеж: ' . json_encode($cashboxBalanceInouts->getErrors()));
            }
        }

        return true;
    }

    /**
     * Обрабатывает баланс поставщика при оплате
     */
    private function processSupplierBalance($payments)
    {
        // Получаем курс доллара
        $dollarCurrency = Currency::findOne(['name' => 'Dollar']);
        $dollarCurrencyId = $dollarCurrency ? $dollarCurrency->id : 2;

        // Получаем текущий курс валюты
        $currencyCourse = CurrencyCourse::find()
            ->where(['currency_id' => 2])
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere(['>', 'end_date', date('Y-m-d')])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        $exchangeRate = $currencyCourse ? $currencyCourse->course : 1;

        // Общая сумма оплаты в долларах
        $totalPaymentInDollars = 0;
        $supplier_id = null;

        // Обрабатываем каждую валюту отдельно
        foreach ($payments['currencies'] as $payment) {
            // Проверяем, что все необходимые поля существуют
            if (!isset($payment['currency_id']) || !isset($payment['sum']) || !isset($payment['suplier_id'])) {
                continue;
            }

            // Запоминаем ID поставщика
            if ($supplier_id === null) {
                $supplier_id = $payment['suplier_id'];
            }

            // Очищаем сумму от пробелов и запятых
            $payment['sum'] = (float)str_replace([' ', ','], ['', '.'], $payment['sum']);

            if (!is_numeric($payment['sum']) || $payment['sum'] <= 0) {
                continue;
            }

            // Конвертируем сумму в доллары, если это не доллары
            $paymentInDollars = $payment['sum'];
            if ($payment['currency_id'] != $dollarCurrencyId && $currencyCourse) {
                $paymentInDollars = $payment['sum'] / $exchangeRate;

            }

            // Добавляем к общей сумме в долларах
            $totalPaymentInDollars += $paymentInDollars;

            // Создаем запись в истории баланса поставщика для текущей валюты
            $supplierBalanceHistory = new SuplierBalanceHistory();
            $supplierBalanceHistory->suplier_id = $payment['suplier_id'];
            $supplierBalanceHistory->user_id = Yii::$app->user->id;
            $supplierBalanceHistory->sum = $payment['sum'];
            $supplierBalanceHistory->currency_id = $payment['currency_id'];
            $supplierBalanceHistory->type = SuplierBalanceHistory::TYPE_EXPENSE;
            $supplierBalanceHistory->created_at = date('Y-m-d H:i:s');
            $supplierBalanceHistory->description = 'Оплата поставщику';

            if (!$supplierBalanceHistory->save()) {
                throw new Exception('Не удалось сохранить историю баланса поставщика: ' . json_encode($supplierBalanceHistory->getErrors()));
            }
        }

        if ($totalPaymentInDollars > 0 && $supplier_id !== null) {
            $dollarBalance = SuplierBalance::findOne([
                'suplier_id' => $supplier_id,
                'currency_id' => $dollarCurrencyId,
            ]);

            if (!$dollarBalance) {
                $dollarBalance = new SuplierBalance([
                    'suplier_id' => $supplier_id,
                    'currency_id' => $dollarCurrencyId,
                    'sum' => 0,
                ]);

                if (!$dollarBalance->save()) {
                    throw new Exception('Не удалось создать долларовый баланс поставщика: ' . json_encode($dollarBalance->getErrors()));
                }
            }


            $dollarBalance->sum -= $totalPaymentInDollars;


            if (!$dollarBalance->save()) {
                throw new Exception('Не удалось обновить долларовый баланс поставщика: ' . json_encode($dollarBalance->getErrors()));
            }

        }

        return true;
    }

    public function actionDetail($id)
    {
        $model = Suplier::findOne($id);
        if (!$model) {
            Yii::$app->session->setFlash('error', 'Supplier not found.');
            return $this->redirect(['index']);
        }

        $suplier_id = $model->id;

        $command = Yii::$app->db->createCommand("
                    SELECT
                        st.name as storage_name,
                        p.name as product_name,
                        su.full_name as suplier_name,
                        s.name as size_name,
                        u2.full_name AS enter_user_name,
                        u3.full_name AS accepted_user_name,
                        u.full_name AS user_name,
                        sbh.quantity,
                        sbh.size AS storage_size,
                        sbh.type AS storage_type,
                        sbh.status AS storage_status,
                        sbh.enter_date,
                        sbh.accepted_date,
                        sbh.created_at AS storage_created_at,

                        u4.full_name AS user_suplier_name,
                        sb.sum AS sum,
                        c.name AS currency_name,
                        sb.type AS balance_type,
                        sb.created_at AS balance_created_at,
                        sb.deleted_at AS balance_deleted_at

                    FROM
                        storage_balance_history as sbh
                    LEFT JOIN suplier_balance_history as sb  ON sbh.suplier_id = sb.suplier_id
                    LEFT JOIN storage as st on sbh.storage_id = st.id
                    LEFT JOIN suplier as su on sbh.suplier_id = su.id
                    LEFT JOIN products as p on sbh.product_id = p.id
                    Left JOIN users as u on sb.user_id = u.id
                    Left join users as u2 on sbh.enter_user_id = u2.id
                    Left join users as u3 on sbh.accepted_user_id = u3.id
                    Left join currency as c on sb.currency_id = c.id
                    Left join sizes as s on sbh.size_id = s.id
                    Left join users as u4 on sb.user_id = u4.id
                    WHERE
                        sbh.suplier_id = :suplier_id
                        OR sb.suplier_id = :suplier_id
                ");

        $command->bindValue(':suplier_id', $suplier_id);
        $result = $command->queryAll();

        return $this->render('detail', [
            'model' => $model,
            'result' => $result,
        ]);
    }

    /**
     * История платежей поставщикам
     */
    public function actionPaymentHistory()
    {
        // Получаем все записи истории платежей поставщикам
        $paymentHistory = SuplierBalanceHistory::find()
            ->joinWith(['suplier', 'user', 'currency'])
            ->where(['suplier_balance_history.deleted_at' => null])
            ->orderBy(['suplier_balance_history.created_at' => SORT_DESC])
            ->all();

        // Получаем записи из истории кассы, связанные с поставщиками
        $cashboxHistory = CashboxBalanceHistory::find()
            ->joinWith(['user', 'currency', 'cashbox'])
            ->where(['module_type' => CashboxBalanceHistory::MODULE_SUPPLIER_TYPE])
            ->andWhere(['cashbox_balance_history.deleted_at' => null])
            ->orderBy(['cashbox_balance_history.created_at' => SORT_DESC])
            ->all();

        // Получаем список поставщиков для фильтра
        $suppliers = Suplier::find()
            ->where(['deleted_at' => null])
            ->orderBy(['full_name' => SORT_ASC])
            ->all();

        // Получаем список валют для фильтра
        $currencies = Currency::find()
            ->where(['deleted_at' => null])
            ->orderBy(['name' => SORT_ASC])
            ->all();

        // Получаем список пользователей для фильтра
        $users = \app\common\models\User::find()
            ->where(['deleted_at' => null])
            ->orderBy(['full_name' => SORT_ASC])
            ->all();

        return $this->render('payment-history', [
            'paymentHistory' => $paymentHistory,
            'cashboxHistory' => $cashboxHistory,
            'suppliers' => $suppliers,
            'currencies' => $currencies,
            'users' => $users,
        ]);
    }

    /**
     * Получить список поставщиков с их балансами
     */
    public function actionGetSuppliersList()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            // Получаем список всех поставщиков
            $suppliers = Suplier::find()
                ->where(['deleted_at' => null])
                ->orderBy(['full_name' => SORT_ASC])
                ->all();

            $result = [];
            foreach ($suppliers as $supplier) {
                // Получаем балансы поставщика по всем валютам
                // Исправляем запрос, указывая полное имя таблицы для столбца deleted_at
                $balances = SuplierBalance::find()
                    ->where(['suplier_id' => $supplier->id])
                    ->andWhere(['suplier_balance.deleted_at' => null])
                    ->joinWith('currency')
                    ->all();

                $balanceInfo = [];
                foreach ($balances as $balance) {
                    $currencyName = $balance->currency ? $balance->currency->name : 'Unknown';
                    $balanceInfo[$currencyName] = $balance->sum;
                }

                // Формируем информацию о поставщике
                $result[] = [
                    'id' => $supplier->id,
                    'name' => $supplier->full_name,
                    'balances' => $balanceInfo
                ];
            }

            return [
                'status' => 'success',
                'suppliers' => $result
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }
}